-- 简化版50个用户数据
USE studentmis_db;

-- 清空现有数据
DELETE FROM user;
ALTER TABLE user AUTO_INCREMENT = 1;

-- 插入50个用户数据（使用简短姓名和较短密码）
INSERT INTO user (user_num, user_name, password, phone, role_id) VALUES
-- 管理员（1个）
('2024140520', '徐浩翔', '2024140520', '18626425051', 2),

-- 教师（7个）
('2024000001', '张明', '2024000001', '13912345678', 1),
('2024000002', '李芳', '2024000002', '13823456789', 1),
('2024000003', '王建', '2024000003', '13734567890', 1),
('2024000004', '陈美', '2024000004', '13645678901', 1),
('2024000005', '刘强', '2024000005', '13556789012', 1),
('2024000006', '赵琴', '2024000006', '13467890123', 1),
('2024000007', '孙华', '2024000007', '13378901234', 1),

-- 学生（42个）
('2024140501', '马浩', '2024140501', '13289012345', 0),
('2024140502', '李雨', '2024140502', '13190123456', 0),
('2024140503', '张远', '2024140503', '13801234567', 0),
('2024140504', '王梅', '2024140504', '13712345678', 0),
('2024140505', '陈杰', '2024140505', '13623456789', 0),
('2024140506', '刘玲', '2024140506', '13534567890', 0),
('2024140507', '杨博', '2024140507', '13445678901', 0),
('2024140508', '赵燕', '2024140508', '13356789012', 0),
('2024140509', '孙明', '2024140509', '13267890123', 0),
('2024140510', '周婷', '2024140510', '13178901234', 0),
('2024140511', '吴华', '2024140511', '13089012345', 0),
('2024140512', '郑娜', '2024140512', '13990123456', 0),
('2024140513', '冯宇', '2024140513', '13891234567', 0),
('2024140514', '何敏', '2024140514', '13792345678', 0),
('2024140515', '许强', '2024140515', '13693456789', 0),
('2024140516', '蒋华', '2024140516', '13594567890', 0),
('2024140517', '韩杰', '2024140517', '13495678901', 0),
('2024140518', '曹芳', '2024140518', '13396789012', 0),
('2024140519', '邓远', '2024140519', '13297890123', 0),
('2024140521', '谢丽', '2024140521', '13198901234', 0),
('2024140522', '罗国', '2024140522', '13099012345', 0),
('2024140523', '梁玲', '2024140523', '13900123456', 0),
('2024140524', '宋华', '2024140524', '13801234568', 0),
('2024140525', '唐琴', '2024140525', '13702345679', 0),
('2024141001', '高博', '2024141001', '13603456780', 0),
('2024141002', '林燕', '2024141002', '13504567891', 0),
('2024141003', '胡明', '2024141003', '13405678902', 0),
('2024141004', '钟婷', '2024141004', '13306789013', 0),
('2024141005', '叶华', '2024141005', '13207890124', 0),
('2024141006', '苏娜', '2024141006', '13108901235', 0),
('2024141007', '潘宇', '2024141007', '13009012346', 0),
('2024141008', '董敏', '2024141008', '13910123457', 0),
('2024141009', '薛强', '2024141009', '13811234568', 0),
('2024141010', '范华', '2024141010', '13712345679', 0),
('2024142001', '石杰', '2024142001', '13613456780', 0),
('2024142002', '姚芳', '2024142002', '13514567891', 0),
('2024142003', '汪远', '2024142003', '13415678902', 0),
('2024142004', '金丽', '2024142004', '13316789013', 0),
('2024142005', '段国', '2024142005', '13217890124', 0),
('2024142006', '白玲', '2024142006', '13118901235', 0),
('2024142007', '康华', '2024142007', '13019012346', 0),
('2024142008', '毛琴', '2024142008', '13920123457', 0),
('2024143001', '严博', '2024143001', '13821234568', 0),
('2024143002', '尹燕', '2024143002', '13722345679', 0),
('2024143003', '常明', '2024143003', '13623456780', 0),
('2024143004', '武婷', '2024143004', '13524567891', 0);

-- 验证插入结果
SELECT 
    COUNT(*) AS '总用户数',
    SUM(CASE WHEN role_id = 2 THEN 1 ELSE 0 END) AS '管理员数',
    SUM(CASE WHEN role_id = 1 THEN 1 ELSE 0 END) AS '教师数',
    SUM(CASE WHEN role_id = 0 THEN 1 ELSE 0 END) AS '学生数'
FROM user;

-- 显示前10个用户
SELECT user_num, user_name, 
    CASE role_id 
        WHEN 0 THEN '学生'
        WHEN 1 THEN '教师' 
        WHEN 2 THEN '管理员'
    END AS '角色'
FROM user 
ORDER BY role_id DESC, user_num 
LIMIT 10;
