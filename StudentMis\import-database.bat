@echo off
echo ========================================
echo   Student Management System - Database Import
echo ========================================
echo.
echo Importing complete database...
echo.
echo Please ensure:
echo 1. MySQL service is running
echo 2. Root password is xhxabc
echo.

mysql -u root -p2847 -e "CREATE DATABASE IF NOT EXISTS studentmis_db;"
mysql -u root -p2847 studentmis_db < "..\studentmis_db.sql"

if %errorlevel% == 0 (
    echo.
    echo Database import successful!
    echo.
    echo Available test accounts:
    echo Admin:   1000 / Lcd123
    echo Teacher: 1123 / Lcd123
    echo Student: 170340 / Lcd123
    echo.
    echo You can now start the server!
) else (
    echo.
    echo Database import failed!
    echo Please check if MySQL service is running and password is correct.
)

echo.
pause
