package com.ntvu.studentmis.db;

import com.ntvu.studentmis.entity.Teacher;
import com.ntvu.studentmis.pager.PagerHelper;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;

/**
 * 对数据库操作的类
 */
public class DBTeacher {
    //驱动类
    private final String driverClassName = "com.mysql.cj.jdbc.Driver";
    //连接数据库地址
    private final String url = "*****************************************";
    //连接数据库用户名
    private final String dbName = "root";
    //连接数据库密码
    private final String dbPwd = "xhxabc";

    //
    private Connection conn = null;
    private Statement stmt = null;
    private String sql = null;
    private ResultSet rs = null;

    /**
     * 初始化数据连接
     */
    private void init(){
        try {
            //加载驱动
            Class.forName(driverClassName);
            //获得与数据库的连接
            this.conn = DriverManager.getConnection(url,dbName,dbPwd);
            //获得招待句柄
            this.stmt = this.conn.createStatement();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 释放连接资源
     */
    private void release()
    {
        //关闭
        try {
            if(rs != null && !rs.isClosed())
            {
                rs.close();
                rs = null;
            }
            if(stmt != null && !stmt.isClosed()) {
                stmt.close();
                stmt = null;
            }
            if(conn != null && !conn.isClosed()) {
                conn.close();
                conn = null;
            }
        } catch (SQLException throwables) {
            throwables.printStackTrace();
        }
    }

    public boolean register(Teacher teacher)
    {
        boolean succeed = false;
        try{
            init();
            sql = "insert into Teacher(tea_num,tea_name,tea_sex,tea_age,tea_course,major,department)" +
                    " values(%s,%s,%s,%d,%s,%s,%s)";
            sql = String.format(sql,
                    "'" + teacher.getTea_num() + "'",
                    "'" + teacher.getTea_name() + "'",
                    "'" + teacher.getTea_sex() + "'",
                    teacher.getTea_age(),
                    "'" + teacher.getTea_course() + "'",
                    "'" + teacher.getMajor() + "'",
                    "'" + teacher.getDepartment() + "'");
            System.out.println(sql);
            //执行，返回受影响的记录行数
            int effectedRows = stmt.executeUpdate(sql);
            //使用结果
            //如果结果集不为空
            succeed = effectedRows > 0;
            //关闭
            release();
        }catch (Exception ex)
        {
            ex.printStackTrace();
        }
        return succeed;
    }

    public boolean delete(int id)
    {
        boolean succeed = false;
        try{
            init();
            sql = "delete from Teacher where tea_id = %d";
            sql = String.format(sql, id);
            System.out.println(sql);
            //执行，返回受影响的记录行数
            int effectedRows = stmt.executeUpdate(sql);
            //使用结果
            if(effectedRows > 0)
            {
                //如果结果集不为空
                succeed = true;
            }else{
                succeed = false;
            }
            //关闭
            release();
        }catch (Exception ex)
        {
            ex.printStackTrace();
        }
        return succeed;
    }

    public boolean edit(Teacher teacher)
    {
        boolean succeed = false;
        try{
            init();
            sql = "update Teacher set tea_num=%s,tea_name=%s,tea_sex=%s,tea_age=%d,tea_course=%s,major=%s,department=%s where tea_id = %d";
            sql = String.format(sql,
                    "'" + teacher.getTea_num() + "'",
                    "'" + teacher.getTea_name() + "'",
                    "'" + teacher.getTea_sex() + "'",
                    teacher.getTea_age(),
                    "'" + teacher.getTea_course() + "'",
                    "'" + teacher.getMajor() + "'",
                    "'" + teacher.getDepartment() + "'",
                    teacher.getTea_id());
            System.out.println(sql);
            //执行，返回受影响的记录行数
            int effectedRows = stmt.executeUpdate(sql);
            //使用结果
            //如果结果集不为空
            succeed = effectedRows > 0;
            //关闭
            release();
        }catch (Exception ex)
        {
            ex.printStackTrace();
        }
        return succeed;
    }

    //编辑删除
    public Teacher getListById(int id)
    {
        Teacher teacher = null;
        try {
            init();
            sql = "select * from teacher where tea_id = %d";
            sql = String.format(sql,id);
            //执行，获得返回结果
            System.out.println(sql);
            ResultSet rs = stmt.executeQuery(sql);
            //使用结果
            if(rs.next())
            {
                //如果结果集不为空
                teacher = new Teacher();
                teacher.setTea_id(rs.getInt("tea_id"));
                teacher.setTea_num(rs.getString("tea_num"));
                teacher.setTea_name(rs.getString("tea_name"));
                teacher.setTea_sex(rs.getString("tea_sex"));
                teacher.setTea_age(rs.getInt("tea_age"));
                teacher.setTea_course(rs.getString("tea_course"));
                teacher.setMajor(rs.getString("major"));
                teacher.setDepartment(rs.getString("department"));
            }
            //关闭
            release();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return teacher;
    }

    public int getRecordCount(String tea_num, String tea_name)
    {
        int count = 0;
        try {
            init();
            sql = "select count(*) from teacher where 1 = 1 ";
            if(tea_num != null && !tea_num.equals(""))
            {
                sql += " and tea_num like '%" + tea_num + "%'";
            }
            if(tea_name != null && !tea_name.equals(""))
            {
                sql += " and tea_name like '%" + tea_name + "%'";
            }
            //执行，获得返回结果
            System.out.println(sql);
            ResultSet rs = stmt.executeQuery(sql);
            //使用结果
            if(rs.next())
            {
                count = rs.getInt(1);
            }
            //关闭
            release();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return count;
    }

    public void getList(PagerHelper<Teacher> pager)
    {
        try {
            init();
            sql = "select * from Teacher where 1 = 1";
            if(pager.getQueryParams().containsKey("tea_num"))
            {
                sql += " and tea_num like '%" + pager.getQueryParams().get("tea_num") + "%'";
            }
            if(pager.getQueryParams().containsKey("tea_name"))
            {
                sql += " and tea_name like '%" + pager.getQueryParams().get("tea_name") + "%'";
            }
            //拼接：limit
            sql += " limit " + (pager.getPageIndex() - 1) * pager.getPageSize() + "," + pager.getPageSize();//limit 3,3
            //执行，获得返回结果
            System.out.println(sql);
            ResultSet rs = stmt.executeQuery(sql);
            //使用结果
            List<Teacher> lst = new ArrayList<>();
            while(rs.next())
            {
                //如果结果集不为空
                Teacher teacher = new Teacher();
                teacher.setTea_id(rs.getInt("tea_id"));
                teacher.setTea_num(rs.getString("tea_num"));
                teacher.setTea_name(rs.getString("tea_name"));
                teacher.setTea_sex(rs.getString("tea_sex"));
                teacher.setTea_age(rs.getInt("tea_age"));
                teacher.setTea_course(rs.getString("tea_course"));
                teacher.setMajor(rs.getString("major"));
                teacher.setDepartment(rs.getString("department"));
                lst.add(teacher);
            }
            pager.setData(lst);
            //关闭
            release();
            //
            int count = getRecordCount(pager.getQueryParams().get("tea_num"),pager.getQueryParams().get("tea_name"));
            pager.setRecordCount(count);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


}
