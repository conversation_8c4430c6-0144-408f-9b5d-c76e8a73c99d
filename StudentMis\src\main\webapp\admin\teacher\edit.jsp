<%@ page import="com.ntvu.studentmis.db.DBTeacher" %>
<%@ page import="com.ntvu.studentmis.entity.Teacher" %>
<%@ page import="java.io.*" %>
<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <jsp:include page="../../include/header_css.jsp" flush="true"/>
    <title>修改教师信息</title>
</head>
<body class="hold-transition sidebar-mini layout-fixed">
<div class="wrapper">
    <%@include file="../../include/header_nav.jsp"%>
    <%@include file="../../include/left_menu.jsp"%>
    <div class="content-wrapper" style="min-height: 1345.6px;">
        <!-- Content Header (Page header) -->
        <section class="content-header">
            <div class="container-fluid">
                <div class="row mb-2">
                    <div class="col-sm-6">
                        <h1>修改教师信息</h1>
                    </div>
                </div>
            </div><!-- /.container-fluid -->
        </section>

        <%
            String id = request.getParameter("id");
            System.out.println(id + ":id");
            Teacher teacher = null;
            if (id != null && !id.trim().isEmpty()) {
                try {
                    teacher = new DBTeacher().getListById(Integer.parseInt(id));
                } catch (NumberFormatException e) {
                    e.printStackTrace();
                    response.sendRedirect("list.jsp");
                    return;
                }
            }
            if (teacher == null) {
                response.sendRedirect("list.jsp");
                return;
            }
        %>
        <!-- Main content -->
        <section class="content">
            <div class="container-fluid">
                <div class="row">
                    <!-- left column -->
                    <div class="col-md-12">
                        <!-- jquery validation -->
                        <div class="card card-primary">
                            <div class="card-header">
                                <h3 class="card-title">请修改<small>教师信息</small></h3>
                            </div>
                            <!-- /.card-header -->
                            <!-- form start -->
                            <form name="form1" id="form1" method="post" action="<%= request.getContextPath() + "/admin/teacher/TeacherServlet?action=edit"%>" onsubmit="return verify()">
                                <input type="hidden" name="id" value="<%= teacher.getTea_id()%>">
                                <div class="card-body">
                                    <div class="form-group">
                                        <label >学号</label>
                                        <input type="text" name="txtNum" class="form-control" value="<%=teacher.getTea_num()%>">
                                    </div>
                                    <div class="form-group">
                                        <label >姓名</label>
                                        <input type="text" name="txtName" class="form-control"  value="<%=teacher.getTea_name()%>">
                                    </div>
                                    <div class="form-group">
                                        <label >性别</label>
                                        <input hidden="hidden" name="txtSex" value="<%=teacher.getTea_sex()%>">
                                        男:<input type="radio" name="rdSex" value="男" id="male">
                                        女:<input type="radio" name="rdSex" value="女" id="female">
                                    </div>
                                    <div class="form-group">
                                        <label >年龄</label>
                                        <input type="text" name="txtAge" class="form-control"  value="<%=teacher.getTea_age()%>">
                                    </div>
                                    <div class="form-group">
                                        <label>所任课程</label>
                                        <input type="text" name="txtCourse" class="form-control"  value="<%=teacher.getTea_course()%>">
                                    </div>
                                    <div class="form-group">
                                        <label >专业</label>
                                        <input type="text" name="txtMajor" class="form-control" value="<%=teacher.getMajor()%>">
                                    </div>
                                    <div class="form-group">
                                        <label>院系</label>
                                        <input type="text" name="txtDepart" class="form-control"  value="<%=teacher.getDepartment()%>">
                                    </div>
                                </div>
                                <!-- /.card-body -->
                                <div class="card-footer">
                                    <input type="submit" class="btn btn-primary" name="btnSubmit" value="提交">
                                </div>
                            </form>
                        </div>
                        <!-- /.card -->
                    </div>
                    <!--/.col (left) -->
                    <!-- right column -->
                    <div class="col-md-6">

                    </div>
                    <!--/.col (right) -->
                </div>
                <!-- /.row -->
            </div><!-- /.container-fluid -->
        </section>
        <!-- /.content -->
    </div>
</div>
<%@include file="../../include/foot_js.jsp"%>
<!-- ./wrapper -->
<script type="text/javascript">
    let txtSex=$(`input[name=txtSex]`).val();
    if(txtSex==='男')
    {
        document.getElementById('male').checked = true;
    }else
    {
        document.getElementById('female').checked = true;
    }
    function verify() {
        let txtNum=$(`input[name=txtNum]`).val();
        if(txtNum==='')
        {
            alert(`学号不能为空`);
            $(`input[name=txtNum]`).focus();//光标选中
            return false;
        }
        let txtName=$(`input[name=txtName]`).val();
        if(txtName==='')
        {
            alert(`姓名不能为空`);
            $(`input[name=txtName]`).focus();//光标选中
            return false;
        }
        let txtAge=$(`input[name=txtAge]`).val();
        if(txtAge==='')
        {
            alert(`年龄不能为空`);
            $(`input[name=txtAge]`).focus();//光标选中
            return false;
        }
        let txtCourse=$(`input[name=txtCourse]`).val();
        if(txtCourse==='')
        {
            alert(`所任课程不能为空`);
            $(`input[name=txtCourse]`).focus();//光标选中
            return false;
        }
        let txtMajor=$(`input[name=txtMajor]`).val();
        if(txtMajor==='')
        {
            alert(`专业不能为空`);
            $(`input[name=txtMajor]`).focus();//光标选中
            return false;
        }
        let txtDepart=$(`input[name=txtDepart]`).val();
        if(txtDepart==='')
        {
            alert(`部门不能为空`);
            $(`input[name=txtDepart]`).focus();//光标选中
            return false;
        }
    }
</script>
</body>
</html>
