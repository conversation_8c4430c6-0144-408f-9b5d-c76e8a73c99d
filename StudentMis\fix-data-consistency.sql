-- 修复数据一致性问题
-- 确保用户表、学生表和成绩表中的数据保持一致

USE studentmis_db;

-- 1. 首先删除不一致的旧数据
DELETE FROM score WHERE stu_num IN ('170340', '170339', '160341');

-- 2. 修复成绩表中的性别不一致问题（张三的性别应该统一为男）
UPDATE score SET stu_sex = '男' WHERE stu_name = '张三' AND stu_sex = '女';
UPDATE score SET stu_sex = '男' WHERE stu_name = '李四' AND stu_sex = '女';

-- 3. 确保成绩表中的学生数据与用户表一致
-- 删除成绩表中不存在于用户表的学生记录
DELETE FROM score WHERE stu_num NOT IN (SELECT user_num FROM user WHERE role_id = 0);

-- 4. 为现有的真实学生添加更多成绩记录，确保数据一致性
-- 马浩 (2024140501) 的成绩记录
INSERT INTO score (stu_num, stu_name, stu_sex, stu_class, course_name, score_grade, major) VALUES
('2024140501', '马浩', '男', '计算机4241班', '高等数学', 88.5, '计算机科学与技术'),
('2024140501', '马浩', '男', '计算机4241班', '线性代数', 85.0, '计算机科学与技术'),
('2024140501', '马浩', '男', '计算机4241班', '大学英语', 82.0, '计算机科学与技术'),
('2024140501', '马浩', '男', '计算机4241班', '计算机网络', 90.5, '计算机科学与技术');

-- 李雨 (2024140502) 的成绩记录
INSERT INTO score (stu_num, stu_name, stu_sex, stu_class, course_name, score_grade, major) VALUES
('2024140502', '李雨', '女', '计算机4241班', '高等数学', 92.0, '计算机科学与技术'),
('2024140502', '李雨', '女', '计算机4241班', '线性代数', 89.5, '计算机科学与技术'),
('2024140502', '李雨', '女', '计算机4241班', '大学英语', 95.0, '计算机科学与技术'),
('2024140502', '李雨', '女', '计算机4241班', '计算机网络', 88.0, '计算机科学与技术');

-- 张远 (2024140503) 的成绩记录
INSERT INTO score (stu_num, stu_name, stu_sex, stu_class, course_name, score_grade, major) VALUES
('2024140503', '张远', '男', '计算机4241班', '高等数学', 78.5, '计算机科学与技术'),
('2024140503', '张远', '男', '计算机4241班', '线性代数', 82.0, '计算机科学与技术'),
('2024140503', '张远', '男', '计算机4241班', '大学英语', 75.5, '计算机科学与技术'),
('2024140503', '张远', '男', '计算机4241班', 'C++程序设计', 80.0, '计算机科学与技术');

-- 王梅 (2024140504) 的成绩记录
INSERT INTO score (stu_num, stu_name, stu_sex, stu_class, course_name, score_grade, major) VALUES
('2024140504', '王梅', '女', '计算机4241班', '高等数学', 85.0, '计算机科学与技术'),
('2024140504', '王梅', '女', '计算机4241班', '线性代数', 87.5, '计算机科学与技术'),
('2024140504', '王梅', '女', '计算机4241班', '大学英语', 90.0, '计算机科学与技术'),
('2024140504', '王梅', '女', '计算机4241班', 'Java程序设计', 86.5, '计算机科学与技术');

-- 陈杰 (2024140505) 的成绩记录
INSERT INTO score (stu_num, stu_name, stu_sex, stu_class, course_name, score_grade, major) VALUES
('2024140505', '陈杰', '男', '计算机4241班', '高等数学', 91.0, '计算机科学与技术'),
('2024140505', '陈杰', '男', '计算机4241班', '线性代数', 88.0, '计算机科学与技术'),
('2024140505', '陈杰', '男', '计算机4241班', '大学英语', 83.5, '计算机科学与技术'),
('2024140505', '陈杰', '男', '计算机4241班', '数据结构与算法', 93.0, '计算机科学与技术');

-- 5. 验证数据一致性
SELECT '用户表中的学生数量:' as info, COUNT(*) as count FROM user WHERE role_id = 0
UNION ALL
SELECT '学生表中的学生数量:', COUNT(*) FROM student
UNION ALL
SELECT '成绩表中的不同学生数量:', COUNT(DISTINCT stu_num) FROM score;

-- 6. 显示修复后的成绩数据样本
SELECT 
    stu_num as '学号',
    stu_name as '姓名', 
    stu_sex as '性别',
    stu_class as '班级',
    course_name as '科目',
    score_grade as '成绩',
    major as '专业'
FROM score 
WHERE stu_num IN ('2024140501', '2024140502', '2024140503', '2024140504', '2024140505')
ORDER BY stu_num, course_name;
