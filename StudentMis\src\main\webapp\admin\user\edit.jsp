<%@ page import="com.ntvu.studentmis.db.DBManager" %>
<%@ page import="com.ntvu.studentmis.entity.User" %>
<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <jsp:include page="../../include/header_css.jsp" flush="true"/>
    <title>修改信息</title>
</head>
<body class="hold-transition sidebar-mini layout-fixed">
<div class="wrapper">
    <%@include file="../../include/header_nav.jsp"%>
    <%@include file="../../include/left_menu.jsp"%>
    <div class="content-wrapper" style="min-height: 1345.6px;">
        <!-- Content Header (Page header) -->
        <section class="content-header">
            <div class="container-fluid">
                <div class="row mb-2">
                    <div class="col-sm-6">
                        <h1>修改用户信息</h1>
                    </div>
                </div>
            </div><!-- /.container-fluid -->
        </section>

        <%
            String id = request.getParameter("id");
            System.out.println(id + ":id");
            User user = null;
            if (id != null && !id.trim().isEmpty()) {
                try {
                    user = new DBManager().getDetails(Integer.parseInt(id));
                } catch (NumberFormatException e) {
                    e.printStackTrace();
                    response.sendRedirect("list.jsp");
                    return;
                }
            }
            if (user == null) {
                response.sendRedirect("list.jsp");
                return;
            }
        %>
        <!-- Main content -->
        <section class="content">
            <div class="container-fluid">
                <div class="row">
                    <!-- left column -->
                    <div class="col-md-12">
                        <!-- jquery validation -->
                        <div class="card card-primary">
                            <div class="card-header">
                                <h3 class="card-title">请修改<small>用户信息</small></h3>
                            </div>
                            <!-- /.card-header -->
                            <!-- form start -->
                            <form name="form1" id="form1" method="post" action="<%= request.getContextPath() + "/admin/user/UserServlet?action=edit"%>" onsubmit="return verify()">
                                <input type="hidden" name="id" value="<%= user.getUser_id()%>">
                                <div class="card-body">
                                    <div class="form-group">
                                        <label for="exampleInputPassword1">用户名</label>
                                        <input type="text" name="txtLoginName" class="form-control" id="exampleInputUser" value="<%= user.getUser_num()%>">
                                    </div>
                                    <div class="form-group">
                                        <%--@declare id="exampleinputpassword1"--%><label for="exampleInputPassword1">真实名称</label>
                                        <input type="text" name="txtRealName" class="form-control" id="exampleInputRealName" value="<%= user.getUser_name()%>">
                                    </div>
                                    <div class="form-group">
                                        <label for="exampleInputPassword1">密码</label>
                                        <input type="text" name="txtLoginPassword" class="form-control" id="exampleInputAge" value="<%= user.getPassword()%>"
                                               pattern="(?=.*\d)(?=.*[a-z])(?=.*[A-Z]).{6,}"
                                               title="密码必须包含至少一个大写字母、一个小写字母和一个数字，且长度至少6位"
                                               required>
                                    </div>
                                    <div class="form-group">
                                        <label for="exampleInputPassword1">手机号</label>
                                        <input type="tel" name="txtTelephone" class="form-control" id="exampleInputTelephone" value="<%= user.getPhone()%>"  pattern="^1[3-9]\d{9}$"
                                               title="请输入11位有效手机号（以13-19开头）"
                                               required>
                                    </div>
                                    <div class="form-group">
                                        <label>身份</label>
                                        <input name="selectVal" hidden="hidden" value="<%= user.getRole_id()%>">
                                        <label for="selectList"></label><select id="selectList" name="selectList" >
                                            <option name="txtRole" value="">请选择身份</option>
                                            <option name="txtRole" value="0">学生</option>
                                            <option name="txtRole" value="1">教师</option>
                                            <option name="txtRole" value="2">管理员</option>
                                        </select>

                                    </div>
                                </div>
                                <!-- /.card-body -->
                                <div class="card-footer">
                                    <input type="submit" class="btn btn-primary" name="btnSubmit" value="提交">
                                </div>
                            </form>
                        </div>
                        <!-- /.card -->
                    </div>
                    <!--/.col (left) -->
                    <!-- right column -->
                    <div class="col-md-6">

                    </div>
                    <!--/.col (right) -->
                </div>
                <!-- /.row -->
            </div><!-- /.container-fluid -->
        </section>
        <!-- /.content -->
    </div>
</div>
<%@include file="../../include/foot_js.jsp"%>
<!-- ./wrapper -->
<script type="text/javascript">
    //设置身份选中项
    let selectVal=$(`input[name=selectVal]`).val();
    console.log(selectVal);
    var numbers = $("#selectList").find("option"); //获取select下拉框的所有值
    for (var j = 0; j < numbers.length; j++) {
        if ($(numbers[j]).val() === selectVal) {
            $(numbers[j]).prop("selected", "selected");
        }
    }
    function verify() {
        //对数据进行检验
        let txtLoginName=$(`input[name=txtLoginName]`).val();
        if(txtLoginName==='')
        {
            alert(`登录名称不能为空`);
            $(`input[name=txtLoginName]`).focus();//光标选中
            return false;
        }
        let txtRealName=$(`input[name=txtRealName]`).val();
        if(txtRealName==='')
        {
            alert(`姓名不能为空`);
            $(`input[name=txtRealName]`).focus();//光标选中
            return false;
        }
        let txtLoginPassword=$(`input[name=txtLoginPassword]`).val();
        if(txtLoginPassword==='')
        {
            alert(`密码不能为空`);
            $(`input[name=txtLoginPassword]`).focus();//光标选中
            return false;
        }
        let txtTelephone=$(`input[name=txtTelephone]`).val();
        if(txtTelephone==='')
        {
            alert(`手机号不能为空`);
            $(`input[name=txtTelephone]`).focus();//光标选中
            return false;
        }
    }
</script>
</body>
</html>
