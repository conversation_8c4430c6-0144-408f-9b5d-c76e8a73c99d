/*
 Navicat Premium Data Transfer

 Source Server         : localhost_3306
 Source Server Type    : MySQL
 Source Server Version : 80029 (8.0.29)
 Source Host           : localhost:3306
 Source Schema         : studentmis_db

 Target Server Type    : MySQL
 Target Server Version : 80029 (8.0.29)
 File Encoding         : 65001

 Date: 19/06/2025 16:59:14
*/

-- 创建数据库
CREATE DATABASE IF NOT EXISTS `studentmis_db` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `studentmis_db`;

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for course
-- ----------------------------
DROP TABLE IF EXISTS `course`;
CREATE TABLE `course`  (
  `course_id` int NOT NULL AUTO_INCREMENT COMMENT '课程编号',
  `course_name` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '课程名',
  `course_credit` int NOT NULL COMMENT '学分',
  `course_hours` int NOT NULL COMMENT '学时',
  `course_teacher` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '任课教师',
  `coursedate` date NOT NULL COMMENT '开课时间',
  PRIMARY KEY (`course_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Records of course
-- ----------------------------
INSERT INTO `course` VALUES (1, 'C++程序设计教程', 3, 60, '汪维清', '2025-06-17');
INSERT INTO `course` VALUES (2, 'java入门基础', 3, 60, '肖兴江', '2025-06-17');
INSERT INTO `course` VALUES (3, '计算机组成原理', 3, 60, '胡继宽', '2025-06-18');
INSERT INTO `course` VALUES (4, '信息检索', 2, 60, '郑蔚', '2025-06-17');
INSERT INTO `course` VALUES (5, '操作系统原理', 4, 60, '丁华峰', '2025-06-15');
INSERT INTO `course` VALUES (6, '管理信息系统', 4, 60, '杜治国', '2025-06-11');
INSERT INTO `course` VALUES (7, '语文', 11, 11, '张三', '2025-06-12');

-- ----------------------------
-- Table structure for role
-- ----------------------------
DROP TABLE IF EXISTS `role`;
CREATE TABLE `role`  (
  `role_id` int NOT NULL COMMENT '角色编号',
  `role_name` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '角色名称',
  `role_info` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '角色信息',
  PRIMARY KEY (`role_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Records of role
-- ----------------------------
INSERT INTO `role` VALUES (0, '学生', '学生查看成绩');
INSERT INTO `role` VALUES (1, '教师', '教师操作');
INSERT INTO `role` VALUES (2, '管理员', '管理员操作');

-- ----------------------------
-- Table structure for score
-- ----------------------------
DROP TABLE IF EXISTS `score`;
CREATE TABLE `score`  (
  `score_id` int NOT NULL AUTO_INCREMENT COMMENT '成绩编号',
  `stu_num` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '学号',
  `stu_name` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '姓名',
  `stu_sex` varchar(4) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '性别',
  `stu_class` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '班级',
  `course_name` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '科目',
  `score_grade` double(11, 2) NOT NULL COMMENT '成绩',
  `major` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '专业',
  `sumScore` double NULL DEFAULT NULL COMMENT '总分',
  `avgScore` double NULL DEFAULT NULL COMMENT '平均分',
  PRIMARY KEY (`score_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 117 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Records of score
-- ----------------------------
INSERT INTO `score` VALUES (1, '170340', '张三', '男', '计科1701', 'java入门基础', 96.50, '计算机', NULL, NULL);
INSERT INTO `score` VALUES (2, '170340', '张三', '男', '计科1701', 'C++程序设计教程', 85.00, '计算机', NULL, NULL);
INSERT INTO `score` VALUES (3, '160341', '王五', '男', '信管1601', 'java入门基础', 62.60, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (4, '160341', '王五', '男', '信管1601', 'C++程序设计教程', 85.00, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (5, '170340', '张三', '男', '计科1701', '计算机组成原理', 69.00, '计算机', NULL, NULL);
INSERT INTO `score` VALUES (6, '170340', '张三', '女', '计科1701', '信息检索', 95.00, '计算机', NULL, NULL);
INSERT INTO `score` VALUES (7, '170340', '张三', '男', '计科1701', '操作系统原理', 89.00, '计算机', NULL, NULL);
INSERT INTO `score` VALUES (9, '160341', '王五', '男', '信管1601', 'java入门基础', 92.00, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (11, '170339', '李四', '男', '计科1701', 'java入门基础', 78.20, '计算机', NULL, NULL);
INSERT INTO `score` VALUES (12, '170339', '李四', '女', '计科1701', '信息检索', 98.00, '计算机', NULL, NULL);
INSERT INTO `score` VALUES (13, '170339', '李四', '男', '计科1701', '计算机组成原理', 76.00, '计算机', NULL, NULL);
INSERT INTO `score` VALUES (14, '170339', '李四', '男', '计科1701', '操作系统原理', 69.80, '计算机', NULL, NULL);
INSERT INTO `score` VALUES (15, '170339', '李四', '男', '计科1701', 'C++程序设计教程', 89.00, '计算机', NULL, NULL);
INSERT INTO `score` VALUES (16, '170343', '陈留', '男', '计科1701', 'java入门基础', 80.50, '计算机', NULL, NULL);
INSERT INTO `score` VALUES (17, '170343', '陈留', '男', '计科1701', '计算机组成原理', 83.50, '计算机', NULL, NULL);
INSERT INTO `score` VALUES (18, '170343', '陈留', '男', '计科1701', 'C++程序设计教程', 82.50, '计算机', NULL, NULL);
INSERT INTO `score` VALUES (19, '170343', '陈留', '男', '计科1701', '操作系统原理', 80.00, '计算机', NULL, NULL);
INSERT INTO `score` VALUES (20, '160342', '盛祎琛', '男', '信管1602', '信息检索', 95.00, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (21, '160342', '盛祎琛', '男', '信管1602', '操作系统原理', 85.00, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (22, '160342', '盛祎琛', '男', '信管1602', '计算机组成原理', 65.00, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (23, '160342', '盛祎琛', '男', '信管1602', 'java入门基础', 95.00, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (24, '160343', '闫玉平', '男', '信管1601', '信息检索', 90.00, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (25, '160343', '闫玉平', '男', '信管1601', '操作系统原理', 64.00, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (26, '160343', '闫玉平', '男', '信管1601', '计算机组成原理', 63.00, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (27, '160343', '闫玉平', '男', '信管1601', 'java入门基础', 70.00, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (28, '160344', '陈淑婷', '女', '信管1601', '信息检索', 88.00, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (29, '160344', '陈淑婷', '女', '信管1601', '操作系统原理', 77.00, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (30, '160344', '陈淑婷', '女', '信管1601', '计算机组成原理', 66.00, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (31, '160344', '陈淑婷', '女', '信管1601', 'java入门基础', 88.00, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (32, '160345', '周梦琪', '女', '信管1601', '信息检索', 95.50, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (33, '160345', '周梦琪', '女', '信管1601', '计算机组成原理', 65.50, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (34, '160345', '周梦琪', '女', '信管1601', 'java入门基础', 78.50, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (35, '160345', '周梦琪', '女', '信管1601', '操作系统原理', 85.50, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (36, '160346', '曾智', '男', '信管1601', '信息检索', 90.90, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (37, '160346', '曾智', '男', '信管1601', '计算机组成原理', 98.90, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (38, '160346', '曾智', '男', '信管1601', 'java入门基础', 60.90, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (39, '160346', '曾智', '男', '信管1601', '操作系统原理', 70.90, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (40, '160347', '百鬼丸', '男', '信管1601', '信息检索', 85.00, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (41, '160347', '百鬼丸', '男', '信管1601', '操作系统原理', 65.00, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (42, '160347', '百鬼丸', '男', '信管1601', 'java入门基础', 75.00, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (43, '160347', '百鬼丸', '男', '信管1601', '计算机组成原理', 95.00, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (44, '160348', '多罗罗', '男', '信管1601', '信息检索', 65.00, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (45, '160349', '伊藤健太郎', '男', '信管1601', '信息检索', 95.00, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (46, '160350', '贺来贤人', '男', '信管1601', '信息检索', 66.00, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (47, '160351', '服部平次', '男', '信管1601', '信息检索', 88.00, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (48, '160352', '柯南', '男', '信管1601', '信息检索', 99.00, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (49, '170353', '时崎狂三', '男', '计科1601', 'java入门基础', 96.50, '计算机', NULL, NULL);
INSERT INTO `score` VALUES (50, '170354', '坂本', '男', '计科1601', 'java入门基础', 66.50, '计算机', NULL, NULL);
INSERT INTO `score` VALUES (51, '170355', '夏瀚宇', '男', '计科1601', 'java入门基础', 99.00, '计算机', NULL, NULL);
INSERT INTO `score` VALUES (52, '170356', '管栎', '男', '计科1602', 'java入门基础', 96.00, '计算机', NULL, NULL);
INSERT INTO `score` VALUES (53, '170357', '连淮伟', '男', '计科1602', 'java入门基础', 76.50, '计算机', NULL, NULL);
INSERT INTO `score` VALUES (54, '170358', '杨芸晴', '女', '计科1603', 'java入门基础', 69.00, '计算机', NULL, NULL);
INSERT INTO `score` VALUES (55, '170359', '赖美云', '男', '计科1603', 'java入门基础', 88.00, '计算机', NULL, NULL);
INSERT INTO `score` VALUES (56, '170360', '灰二', '男', '计科1603', 'java入门基础', 96.00, '计算机', NULL, NULL);
INSERT INTO `score` VALUES (57, '160352', '潘思言', '男', '信管1701', '信息检索', 95.00, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (58, '160352', '杨皓鹏', '男', '信管1701', '信息检索', 90.00, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (59, '160352', '王子', '男', '信管1701', '信息检索', 93.50, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (60, '160352', '宫崎骏', '男', '信管1801', '信息检索', 75.00, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (61, '160352', '胡春阳', '男', '信管1801', '信息检索', 66.00, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (62, '160352', '阿云嘎', '男', '信管1801', '信息检索', 78.00, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (63, '160352', '吴磊', '男', '信管1703', '信息检索', 79.00, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (64, '160352', '魏大勋', '男', '信管1703', '信息检索', 65.00, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (65, '160352', '白敬亭', '男', '信管1703', '信息检索', 75.00, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (66, '160352', '铃屋十造', '男', '信管1802', '信息检索', 65.00, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (67, '160348', '多罗罗', '男', '信管1601', 'java入门基础', 65.00, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (68, '160349', '伊藤健太郎', '男', '信管1601', 'java入门基础', 95.00, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (69, '160350', '贺来贤人', '男', '信管1601', 'java入门基础', 66.00, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (70, '160351', '服部平次', '男', '信管1601', 'java入门基础', 88.00, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (71, '160352', '柯南', '男', '信管1601', 'java入门基础', 99.00, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (72, '170353', '时崎狂三', '男', '计科1601', '操作系统原理', 96.50, '计算机', NULL, NULL);
INSERT INTO `score` VALUES (73, '170354', '坂本', '男', '计科1601', '操作系统原理', 66.50, '计算机', NULL, NULL);
INSERT INTO `score` VALUES (74, '170355', '夏瀚宇', '男', '计科1601', '操作系统原理', 99.00, '计算机', NULL, NULL);
INSERT INTO `score` VALUES (75, '170356', '管栎', '男', '计科1602', '操作系统原理', 96.00, '计算机', NULL, NULL);
INSERT INTO `score` VALUES (76, '170357', '连淮伟', '男', '计科1602', '操作系统原理', 76.50, '计算机', NULL, NULL);
INSERT INTO `score` VALUES (77, '170358', '杨芸晴', '女', '计科1603', '操作系统原理', 69.00, '计算机', NULL, NULL);
INSERT INTO `score` VALUES (78, '170359', '赖美云', '男', '计科1603', '操作系统原理', 88.00, '计算机', NULL, NULL);
INSERT INTO `score` VALUES (79, '170360', '灰二', '男', '计科1603', '操作系统原理', 96.00, '计算机', NULL, NULL);
INSERT INTO `score` VALUES (80, '160352', '潘思言', '男', '信管1701', 'java入门基础', 95.00, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (81, '160352', '杨皓鹏', '男', '信管1701', 'java入门基础', 90.00, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (82, '160352', '王子', '男', '信管1701', 'java入门基础', 93.50, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (83, '160352', '宫崎骏', '男', '信管1801', 'java入门基础', 75.00, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (84, '160352', '胡春阳', '男', '信管1801', 'java入门基础', 66.00, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (85, '160352', '阿云嘎', '男', '信管1801', 'java入门基础', 78.00, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (86, '160352', '吴磊', '男', '信管1703', 'java入门基础', 79.00, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (87, '160352', '魏大勋', '男', '信管1703', 'java入门基础', 65.00, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (88, '160352', '白敬亭', '男', '信管1703', 'java入门基础', 75.00, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (89, '160352', '铃屋十造', '男', '信管1802', 'java入门基础', 65.00, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (90, '160348', '多罗罗', '男', '信管1601', '计算机组成原理', 65.00, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (91, '160349', '伊藤健太郎', '男', '信管1601', '计算机组成原理', 95.00, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (92, '160350', '贺来贤人', '男', '信管1601', '计算机组成原理', 66.00, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (93, '160351', '服部平次', '男', '信管1601', '计算机组成原理', 88.00, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (94, '160352', '柯南', '男', '信管1601', '计算机组成原理', 99.00, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (95, '170353', '时崎狂三', '男', '计科1601', 'C++程序设计教程', 96.50, '计算机', NULL, NULL);
INSERT INTO `score` VALUES (96, '170354', '坂本', '男', '计科1601', 'C++程序设计教程', 66.50, '计算机', NULL, NULL);
INSERT INTO `score` VALUES (97, '170355', '夏瀚宇', '男', '计科1601', 'C++程序设计教程', 99.00, '计算机', NULL, NULL);
INSERT INTO `score` VALUES (98, '170356', '管栎', '男', '计科1602', 'C++程序设计教程', 96.00, '计算机', NULL, NULL);
INSERT INTO `score` VALUES (99, '170357', '连淮伟', '男', '计科1602', 'C++程序设计教程', 76.50, '计算机', NULL, NULL);
INSERT INTO `score` VALUES (100, '170358', '杨芸晴', '男', '计科1603', 'C++程序设计教程', 69.00, '计算机', NULL, NULL);
INSERT INTO `score` VALUES (101, '170359', '赖美云', '男', '计科1603', 'C++程序设计教程', 88.00, '计算机', NULL, NULL);
INSERT INTO `score` VALUES (102, '170360', '灰二', '男', '计科1603', 'C++程序设计教程', 96.00, '计算机', NULL, NULL);
INSERT INTO `score` VALUES (103, '160352', '潘思言', '男', '信管1701', '计算机组成原理', 95.00, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (104, '160352', '杨皓鹏', '男', '信管1701', '计算机组成原理', 90.00, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (105, '160352', '王子', '男', '信管1701', '计算机组成原理', 93.50, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (106, '160352', '宫崎骏', '男', '信管1801', '计算机组成原理', 75.00, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (107, '160352', '胡春阳', '男', '信管1801', '计算机组成原理', 66.00, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (108, '160352', '阿云嘎', '男', '信管1801', '计算机组成原理', 78.00, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (109, '160352', '吴磊', '男', '信管1703', '计算机组成原理', 79.00, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (110, '160352', '魏大勋', '男', '信管1703', '计算机组成原理', 65.00, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (111, '160352', '白敬亭', '男', '信管1703', '计算机组成原理', 75.00, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (113, '11', '小白', '男', '软件221', 'VUE', 100.00, '软件', NULL, NULL);
INSERT INTO `score` VALUES (114, '11', '小米', '男', '软件221', 'VUE', 100.00, '软件', NULL, NULL);
INSERT INTO `score` VALUES (115, '12', '语文', 'null', '男', 'VUE', 100.00, '软件', NULL, NULL);
INSERT INTO `score` VALUES (116, '13', '语文', '男', '软件221', 'VUE', 100.00, '软件', NULL, NULL);

-- ----------------------------
-- Table structure for student
-- ----------------------------
DROP TABLE IF EXISTS `student`;
CREATE TABLE `student`  (
  `stu_id` int NOT NULL AUTO_INCREMENT COMMENT '编号',
  `stu_num` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '账号',
  `stu_name` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '姓名',
  `stu_sex` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '性别',
  `stu_age` int NOT NULL COMMENT '年龄',
  `stu_class` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '班级',
  `major` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '专业',
  `department` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '院系',
  PRIMARY KEY (`stu_id`) USING BTREE,
  UNIQUE INDEX `stu_num`(`stu_num` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 41 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Records of student (42个学生，与user表中的学生用户对应)
-- ----------------------------
-- 计算机科学与技术专业（140开头）
INSERT INTO `student` VALUES (1, '2024140501', '马浩', '男', 20, '计算机4241班', '计算机科学与技术', '计算机工程学院');
INSERT INTO `student` VALUES (2, '2024140502', '李雨', '女', 20, '计算机4241班', '计算机科学与技术', '计算机工程学院');
INSERT INTO `student` VALUES (3, '2024140503', '张远', '男', 20, '计算机4241班', '计算机科学与技术', '计算机工程学院');
INSERT INTO `student` VALUES (4, '2024140504', '王梅', '女', 20, '计算机4241班', '计算机科学与技术', '计算机工程学院');
INSERT INTO `student` VALUES (5, '2024140505', '陈杰', '男', 20, '计算机4241班', '计算机科学与技术', '计算机工程学院');
INSERT INTO `student` VALUES (6, '2024140506', '刘玲', '女', 20, '计算机4241班', '计算机科学与技术', '计算机工程学院');
INSERT INTO `student` VALUES (7, '2024140507', '杨博', '男', 20, '计算机4241班', '计算机科学与技术', '计算机工程学院');
INSERT INTO `student` VALUES (8, '2024140508', '赵燕', '女', 20, '计算机4241班', '计算机科学与技术', '计算机工程学院');
INSERT INTO `student` VALUES (9, '2024140509', '孙明', '男', 20, '计算机4241班', '计算机科学与技术', '计算机工程学院');
INSERT INTO `student` VALUES (10, '2024140510', '周婷', '女', 20, '计算机4241班', '计算机科学与技术', '计算机工程学院');
INSERT INTO `student` VALUES (11, '2024140511', '吴华', '男', 20, '计算机4241班', '计算机科学与技术', '计算机工程学院');
INSERT INTO `student` VALUES (12, '2024140512', '郑娜', '女', 20, '计算机4241班', '计算机科学与技术', '计算机工程学院');
INSERT INTO `student` VALUES (13, '2024140513', '冯宇', '男', 20, '计算机4241班', '计算机科学与技术', '计算机工程学院');
INSERT INTO `student` VALUES (14, '2024140514', '何敏', '女', 20, '计算机4241班', '计算机科学与技术', '计算机工程学院');
INSERT INTO `student` VALUES (15, '2024140515', '许强', '男', 20, '计算机4241班', '计算机科学与技术', '计算机工程学院');
INSERT INTO `student` VALUES (16, '2024140516', '蒋华', '女', 20, '计算机4241班', '计算机科学与技术', '计算机工程学院');
INSERT INTO `student` VALUES (17, '2024140517', '韩杰', '男', 20, '计算机4241班', '计算机科学与技术', '计算机工程学院');
INSERT INTO `student` VALUES (18, '2024140518', '曹芳', '女', 20, '计算机4241班', '计算机科学与技术', '计算机工程学院');
INSERT INTO `student` VALUES (19, '2024140519', '邓远', '男', 20, '计算机4241班', '计算机科学与技术', '计算机工程学院');
INSERT INTO `student` VALUES (20, '2024140521', '谢丽', '女', 20, '计算机4241班', '计算机科学与技术', '计算机工程学院');
INSERT INTO `student` VALUES (21, '2024140522', '罗国', '男', 20, '计算机4241班', '计算机科学与技术', '计算机工程学院');
INSERT INTO `student` VALUES (22, '2024140523', '梁玲', '女', 20, '计算机4241班', '计算机科学与技术', '计算机工程学院');
INSERT INTO `student` VALUES (23, '2024140524', '宋华', '男', 20, '计算机4241班', '计算机科学与技术', '计算机工程学院');
INSERT INTO `student` VALUES (24, '2024140525', '唐琴', '女', 20, '计算机4241班', '计算机科学与技术', '计算机工程学院');

-- 软件工程专业（141开头）
INSERT INTO `student` VALUES (25, '2024141001', '高博', '男', 20, '软件4241班', '软件工程', '计算机工程学院');
INSERT INTO `student` VALUES (26, '2024141002', '林燕', '女', 20, '软件4241班', '软件工程', '计算机工程学院');
INSERT INTO `student` VALUES (27, '2024141003', '胡明', '男', 20, '软件4241班', '软件工程', '计算机工程学院');
INSERT INTO `student` VALUES (28, '2024141004', '钟婷', '女', 20, '软件4241班', '软件工程', '计算机工程学院');
INSERT INTO `student` VALUES (29, '2024141005', '叶华', '男', 20, '软件4241班', '软件工程', '计算机工程学院');
INSERT INTO `student` VALUES (30, '2024141006', '苏娜', '女', 20, '软件4241班', '软件工程', '计算机工程学院');
INSERT INTO `student` VALUES (31, '2024141007', '潘宇', '男', 20, '软件4241班', '软件工程', '计算机工程学院');
INSERT INTO `student` VALUES (32, '2024141008', '董敏', '女', 20, '软件4241班', '软件工程', '计算机工程学院');
INSERT INTO `student` VALUES (33, '2024141009', '薛强', '男', 20, '软件4241班', '软件工程', '计算机工程学院');
INSERT INTO `student` VALUES (34, '2024141010', '范华', '女', 20, '软件4241班', '软件工程', '计算机工程学院');

-- 信息管理与信息系统专业（142开头）
INSERT INTO `student` VALUES (35, '2024142001', '石杰', '男', 20, '信管4241班', '信息管理与信息系统', '计算机工程学院');
INSERT INTO `student` VALUES (36, '2024142002', '姚芳', '女', 20, '信管4241班', '信息管理与信息系统', '计算机工程学院');
INSERT INTO `student` VALUES (37, '2024142003', '汪远', '男', 20, '信管4241班', '信息管理与信息系统', '计算机工程学院');
INSERT INTO `student` VALUES (38, '2024142004', '金丽', '女', 20, '信管4241班', '信息管理与信息系统', '计算机工程学院');
INSERT INTO `student` VALUES (39, '2024142005', '段国', '男', 20, '信管4241班', '信息管理与信息系统', '计算机工程学院');
INSERT INTO `student` VALUES (40, '2024142006', '白玲', '女', 20, '信管4241班', '信息管理与信息系统', '计算机工程学院');
INSERT INTO `student` VALUES (41, '2024142007', '康华', '男', 20, '信管4241班', '信息管理与信息系统', '计算机工程学院');
INSERT INTO `student` VALUES (42, '2024142008', '毛琴', '女', 20, '信管4241班', '信息管理与信息系统', '计算机工程学院');

-- ----------------------------
-- Table structure for teacher
-- ----------------------------
DROP TABLE IF EXISTS `teacher`;
CREATE TABLE `teacher`  (
  `tea_id` int NOT NULL AUTO_INCREMENT COMMENT '编号',
  `tea_num` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '工号',
  `tea_name` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '姓名',
  `tea_sex` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '性别',
  `tea_age` int NOT NULL COMMENT '年龄',
  `tea_course` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '所任课程',
  `major` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '专业',
  `department` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '院系',
  PRIMARY KEY (`tea_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Records of teacher (7个教师，与user表中的教师用户对应)
-- ----------------------------
INSERT INTO `teacher` VALUES (1, '2024000001', '张明', '男', 35, 'Java程序设计', '计算机科学与技术', '计算机工程学院');
INSERT INTO `teacher` VALUES (2, '2024000002', '李芳', '女', 32, 'C++程序设计', '计算机科学与技术', '计算机工程学院');
INSERT INTO `teacher` VALUES (3, '2024000003', '王建', '男', 38, '数据结构与算法', '计算机科学与技术', '计算机工程学院');
INSERT INTO `teacher` VALUES (4, '2024000004', '陈美', '女', 30, '数据库原理', '信息管理与信息系统', '计算机工程学院');
INSERT INTO `teacher` VALUES (5, '2024000005', '刘强', '男', 42, '操作系统原理', '计算机科学与技术', '计算机工程学院');
INSERT INTO `teacher` VALUES (6, '2024000006', '赵琴', '女', 36, '软件工程', '软件工程', '计算机工程学院');
INSERT INTO `teacher` VALUES (7, '2024000007', '孙华', '男', 40, '计算机网络', '计算机科学与技术', '计算机工程学院');

-- ----------------------------
-- Table structure for user
-- ----------------------------
DROP TABLE IF EXISTS `user`;
CREATE TABLE `user`  (
  `user_id` int NOT NULL AUTO_INCREMENT COMMENT '用户编号',
  `user_num` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '用户账号',
  `user_name` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '用户名',
  `password` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '密码',
  `phone` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '用户电话',
  `role_id` int NOT NULL COMMENT '角色编号（外键）',
  PRIMARY KEY (`user_id`, `user_num`) USING BTREE,
  CONSTRAINT `chk_phone` CHECK (regexp_like(`phone`,_utf8mb3'^1[3-9][0-9]{9}$'))
) ENGINE = InnoDB AUTO_INCREMENT = 47 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Records of user (50个真实用户数据)
-- 学号格式：10位数字，密码与学号一致，真实姓名和手机号
-- ----------------------------
-- 超级管理员
INSERT INTO `user` VALUES (1, '2024010000', '管理员', '2024010000', '13800138000', 2);

-- 教师账号（7个）
INSERT INTO `user` VALUES (2, '2024000001', '张明', '2024000001', '13912345678', 1);
INSERT INTO `user` VALUES (3, '2024000002', '李芳', '2024000002', '13823456789', 1);
INSERT INTO `user` VALUES (4, '2024000003', '王建', '2024000003', '13734567890', 1);
INSERT INTO `user` VALUES (5, '2024000004', '陈美', '2024000004', '13645678901', 1);
INSERT INTO `user` VALUES (6, '2024000005', '刘强', '2024000005', '13556789012', 1);
INSERT INTO `user` VALUES (7, '2024000006', '赵琴', '2024000006', '13467890123', 1);
INSERT INTO `user` VALUES (8, '2024000007', '孙华', '2024000007', '13378901234', 1);

-- 学生账号（42个）
-- 计算机科学与技术专业（140开头）
INSERT INTO `user` VALUES (9, '2024140501', '马浩', '2024140501', '13289012345', 0);
INSERT INTO `user` VALUES (10, '2024140502', '李雨', '2024140502', '13190123456', 0);
INSERT INTO `user` VALUES (11, '2024140503', '张远', '2024140503', '13801234567', 0);
INSERT INTO `user` VALUES (12, '2024140504', '王梅', '2024140504', '13712345678', 0);
INSERT INTO `user` VALUES (13, '2024140505', '陈杰', '2024140505', '13623456789', 0);
INSERT INTO `user` VALUES (14, '2024140506', '刘玲', '2024140506', '13534567890', 0);
INSERT INTO `user` VALUES (15, '2024140507', '杨博', '2024140507', '13445678901', 0);
INSERT INTO `user` VALUES (16, '2024140508', '赵燕', '2024140508', '13356789012', 0);
INSERT INTO `user` VALUES (17, '2024140509', '孙明', '2024140509', '13267890123', 0);
INSERT INTO `user` VALUES (18, '2024140510', '周婷', '2024140510', '13178901234', 0);
INSERT INTO `user` VALUES (19, '2024140511', '吴华', '2024140511', '13089012345', 0);
INSERT INTO `user` VALUES (20, '2024140512', '郑娜', '2024140512', '13990123456', 0);
INSERT INTO `user` VALUES (21, '2024140513', '冯宇', '2024140513', '13891234567', 0);
INSERT INTO `user` VALUES (22, '2024140514', '何敏', '2024140514', '13792345678', 0);
INSERT INTO `user` VALUES (23, '2024140515', '许强', '2024140515', '13693456789', 0);
INSERT INTO `user` VALUES (24, '2024140516', '蒋华', '2024140516', '13594567890', 0);
INSERT INTO `user` VALUES (25, '2024140517', '韩杰', '2024140517', '13495678901', 0);
INSERT INTO `user` VALUES (26, '2024140518', '曹芳', '2024140518', '13396789012', 0);
INSERT INTO `user` VALUES (27, '2024140519', '邓远', '2024140519', '13297890123', 0);
INSERT INTO `user` VALUES (28, '2024140521', '谢丽', '2024140521', '13198901234', 0);
INSERT INTO `user` VALUES (29, '2024140522', '罗国', '2024140522', '13099012345', 0);
INSERT INTO `user` VALUES (30, '2024140523', '梁玲', '2024140523', '13900123456', 0);
INSERT INTO `user` VALUES (31, '2024140524', '宋华', '2024140524', '13801234568', 0);
INSERT INTO `user` VALUES (32, '2024140525', '唐琴', '2024140525', '13702345679', 0);

-- 软件工程专业（141开头）
INSERT INTO `user` VALUES (33, '2024141001', '高博', '2024141001', '13603456780', 0);
INSERT INTO `user` VALUES (34, '2024141002', '林燕', '2024141002', '13504567891', 0);
INSERT INTO `user` VALUES (35, '2024141003', '胡明', '2024141003', '13405678902', 0);
INSERT INTO `user` VALUES (36, '2024141004', '钟婷', '2024141004', '13306789013', 0);
INSERT INTO `user` VALUES (37, '2024141005', '叶华', '2024141005', '13207890124', 0);
INSERT INTO `user` VALUES (38, '2024141006', '苏娜', '2024141006', '13108901235', 0);
INSERT INTO `user` VALUES (39, '2024141007', '潘宇', '2024141007', '13009012346', 0);
INSERT INTO `user` VALUES (40, '2024141008', '董敏', '2024141008', '13910123457', 0);
INSERT INTO `user` VALUES (41, '2024141009', '薛强', '2024141009', '13811234568', 0);
INSERT INTO `user` VALUES (42, '2024141010', '范华', '2024141010', '13712345679', 0);

-- 信息管理与信息系统专业（142开头）
INSERT INTO `user` VALUES (43, '2024142001', '石杰', '2024142001', '13613456780', 0);
INSERT INTO `user` VALUES (44, '2024142002', '姚芳', '2024142002', '13514567891', 0);
INSERT INTO `user` VALUES (45, '2024142003', '汪远', '2024142003', '13415678902', 0);
INSERT INTO `user` VALUES (46, '2024142004', '金丽', '2024142004', '13316789013', 0);
INSERT INTO `user` VALUES (47, '2024142005', '段国', '2024142005', '13217890124', 0);
INSERT INTO `user` VALUES (48, '2024142006', '白玲', '2024142006', '13118901235', 0);
INSERT INTO `user` VALUES (49, '2024142007', '康华', '2024142007', '13019012346', 0);
INSERT INTO `user` VALUES (50, '2024142008', '毛琴', '2024142008', '13920123457', 0);

-- ----------------------------
-- Triggers structure for table student
-- ----------------------------
DROP TRIGGER IF EXISTS `stuLogin`;
delimiter ;;
CREATE TRIGGER `stuLogin` AFTER DELETE ON `student` FOR EACH ROW begin 
delete from user where user_num=old.stu_num;
end
;;
delimiter ;

-- ----------------------------
-- Triggers structure for table teacher
-- ----------------------------
DROP TRIGGER IF EXISTS `teaLogin`;
delimiter ;;
CREATE TRIGGER `teaLogin` AFTER DELETE ON `teacher` FOR EACH ROW begin
delete from user where user_num=old.tea_num;
end
;;
delimiter ;

SET FOREIGN_KEY_CHECKS = 1;
