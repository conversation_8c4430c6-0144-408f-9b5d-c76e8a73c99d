/*
 Navicat Premium Data Transfer

 Source Server         : localhost_3306
 Source Server Type    : MySQL
 Source Server Version : 80029 (8.0.29)
 Source Host           : localhost:3306
 Source Schema         : studentmis_db

 Target Server Type    : MySQL
 Target Server Version : 80029 (8.0.29)
 File Encoding         : 65001

 Date: 19/06/2025 16:59:14
*/

-- 创建数据库
CREATE DATABASE IF NOT EXISTS `studentmis_db` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `studentmis_db`;

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for course
-- ----------------------------
DROP TABLE IF EXISTS `course`;
CREATE TABLE `course`  (
  `course_id` int NOT NULL AUTO_INCREMENT COMMENT '课程编号',
  `course_name` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '课程名',
  `course_credit` int NOT NULL COMMENT '学分',
  `course_hours` int NOT NULL COMMENT '学时',
  `course_teacher` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '任课教师',
  `coursedate` date NOT NULL COMMENT '开课时间',
  PRIMARY KEY (`course_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Records of course
-- ----------------------------
INSERT INTO `course` VALUES (1, 'C++程序设计教程', 3, 60, '汪维清', '2025-06-17');
INSERT INTO `course` VALUES (2, 'java入门基础', 3, 60, '肖兴江', '2025-06-17');
INSERT INTO `course` VALUES (3, '计算机组成原理', 3, 60, '胡继宽', '2025-06-18');
INSERT INTO `course` VALUES (4, '信息检索', 2, 60, '郑蔚', '2025-06-17');
INSERT INTO `course` VALUES (5, '操作系统原理', 4, 60, '丁华峰', '2025-06-15');
INSERT INTO `course` VALUES (6, '管理信息系统', 4, 60, '杜治国', '2025-06-11');
INSERT INTO `course` VALUES (7, '语文', 11, 11, '张三', '2025-06-12');

-- ----------------------------
-- Table structure for role
-- ----------------------------
DROP TABLE IF EXISTS `role`;
CREATE TABLE `role`  (
  `role_id` int NOT NULL COMMENT '角色编号',
  `role_name` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '角色名称',
  `role_info` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '角色信息',
  PRIMARY KEY (`role_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Records of role
-- ----------------------------
INSERT INTO `role` VALUES (0, '学生', '学生查看成绩');
INSERT INTO `role` VALUES (1, '教师', '教师操作');
INSERT INTO `role` VALUES (2, '管理员', '管理员操作');

-- ----------------------------
-- Table structure for score
-- ----------------------------
DROP TABLE IF EXISTS `score`;
CREATE TABLE `score`  (
  `score_id` int NOT NULL AUTO_INCREMENT COMMENT '成绩编号',
  `stu_num` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '学号',
  `stu_name` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '姓名',
  `stu_sex` varchar(4) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '性别',
  `stu_class` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '班级',
  `course_name` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '科目',
  `score_grade` double(11, 2) NOT NULL COMMENT '成绩',
  `major` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '专业',
  `sumScore` double NULL DEFAULT NULL COMMENT '总分',
  `avgScore` double NULL DEFAULT NULL COMMENT '平均分',
  PRIMARY KEY (`score_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 117 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Records of score (基于student表的一致性数据)
-- ----------------------------


-- 基于student表的一致性成绩数据
-- 计算机科学与技术专业学生成绩（2024140501-2024140525）
INSERT INTO `score` VALUES (1, '2024140501', '马浩', '男', '计算机4241班', 'Java程序设计', 85.00, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (2, '2024140501', '马浩', '男', '计算机4241班', 'C++程序设计', 78.50, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (3, '2024140501', '马浩', '男', '计算机4241班', '数据结构与算法', 92.00, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (4, '2024140501', '马浩', '男', '计算机4241班', '数据库原理', 88.00, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (5, '2024140501', '马浩', '男', '计算机4241班', '操作系统原理', 82.50, '计算机科学与技术', NULL, NULL);

INSERT INTO `score` VALUES (6, '2024140502', '李雨', '女', '计算机4241班', 'Java程序设计', 90.00, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (7, '2024140502', '李雨', '女', '计算机4241班', 'C++程序设计', 87.50, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (8, '2024140502', '李雨', '女', '计算机4241班', '数据结构与算法', 94.00, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (9, '2024140502', '李雨', '女', '计算机4241班', '数据库原理', 91.50, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (10, '2024140502', '李雨', '女', '计算机4241班', '操作系统原理', 89.00, '计算机科学与技术', NULL, NULL);

INSERT INTO `score` VALUES (11, '2024140503', '张远', '男', '计算机4241班', 'Java程序设计', 76.00, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (12, '2024140503', '张远', '男', '计算机4241班', 'C++程序设计', 73.50, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (13, '2024140503', '张远', '男', '计算机4241班', '数据结构与算法', 84.00, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (14, '2024140503', '张远', '男', '计算机4241班', '数据库原理', 79.00, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (15, '2024140503', '张远', '男', '计算机4241班', '操作系统原理', 81.50, '计算机科学与技术', NULL, NULL);

INSERT INTO `score` VALUES (16, '2024140504', '王芳', '女', '计算机4241班', 'Java程序设计', 88.50, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (17, '2024140504', '王芳', '女', '计算机4241班', 'C++程序设计', 85.00, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (18, '2024140504', '王芳', '女', '计算机4241班', '数据结构与算法', 91.00, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (19, '2024140504', '王芳', '女', '计算机4241班', '数据库原理', 87.50, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (20, '2024140504', '王芳', '女', '计算机4241班', '操作系统原理', 89.50, '计算机科学与技术', NULL, NULL);

INSERT INTO `score` VALUES (21, '2024140505', '陈明', '男', '计算机4241班', 'Java程序设计', 82.00, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (22, '2024140505', '陈明', '男', '计算机4241班', 'C++程序设计', 79.50, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (23, '2024140505', '陈明', '男', '计算机4241班', '数据结构与算法', 86.00, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (24, '2024140505', '陈明', '男', '计算机4241班', '数据库原理', 83.50, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (25, '2024140505', '陈明', '男', '计算机4241班', '操作系统原理', 85.00, '计算机科学与技术', NULL, NULL);

INSERT INTO `score` VALUES (26, '2024140506', '刘静', '女', '计算机4241班', 'Java程序设计', 93.00, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (27, '2024140506', '刘静', '女', '计算机4241班', 'C++程序设计', 90.50, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (28, '2024140506', '刘静', '女', '计算机4241班', '数据结构与算法', 95.00, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (29, '2024140506', '刘静', '女', '计算机4241班', '数据库原理', 92.50, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (30, '2024140506', '刘静', '女', '计算机4241班', '操作系统原理', 91.00, '计算机科学与技术', NULL, NULL);

INSERT INTO `score` VALUES (31, '2024140507', '黄强', '男', '计算机4241班', 'Java程序设计', 77.50, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (32, '2024140507', '黄强', '男', '计算机4241班', 'C++程序设计', 74.00, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (33, '2024140507', '黄强', '男', '计算机4241班', '数据结构与算法', 81.50, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (34, '2024140507', '黄强', '男', '计算机4241班', '数据库原理', 78.00, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (35, '2024140507', '黄强', '男', '计算机4241班', '操作系统原理', 80.50, '计算机科学与技术', NULL, NULL);

INSERT INTO `score` VALUES (36, '2024140508', '周丽', '女', '计算机4241班', 'Java程序设计', 86.00, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (37, '2024140508', '周丽', '女', '计算机4241班', 'C++程序设计', 83.50, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (38, '2024140508', '周丽', '女', '计算机4241班', '数据结构与算法', 89.00, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (39, '2024140508', '周丽', '女', '计算机4241班', '数据库原理', 85.50, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (40, '2024140508', '周丽', '女', '计算机4241班', '操作系统原理', 87.50, '计算机科学与技术', NULL, NULL);

INSERT INTO `score` VALUES (41, '2024140509', '吴涛', '男', '计算机4241班', 'Java程序设计', 84.50, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (42, '2024140509', '吴涛', '男', '计算机4241班', 'C++程序设计', 81.00, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (43, '2024140509', '吴涛', '男', '计算机4241班', '数据结构与算法', 87.50, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (44, '2024140509', '吴涛', '男', '计算机4241班', '数据库原理', 84.00, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (45, '2024140509', '吴涛', '男', '计算机4241班', '操作系统原理', 86.00, '计算机科学与技术', NULL, NULL);

INSERT INTO `score` VALUES (46, '2024140510', '赵敏', '女', '计算机4241班', 'Java程序设计', 91.50, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (47, '2024140510', '赵敏', '女', '计算机4241班', 'C++程序设计', 88.00, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (48, '2024140510', '赵敏', '女', '计算机4241班', '数据结构与算法', 93.50, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (49, '2024140510', '赵敏', '女', '计算机4241班', '数据库原理', 90.00, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (50, '2024140510', '赵敏', '女', '计算机4241班', '操作系统原理', 92.00, '计算机科学与技术', NULL, NULL);

INSERT INTO `score` VALUES (51, '2024140511', '孙杰', '男', '计算机4241班', 'Java程序设计', 79.00, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (52, '2024140511', '孙杰', '男', '计算机4241班', 'C++程序设计', 76.50, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (53, '2024140511', '孙杰', '男', '计算机4241班', '数据结构与算法', 83.00, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (54, '2024140511', '孙杰', '男', '计算机4241班', '数据库原理', 80.50, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (55, '2024140511', '孙杰', '男', '计算机4241班', '操作系统原理', 82.00, '计算机科学与技术', NULL, NULL);

INSERT INTO `score` VALUES (56, '2024140512', '林娜', '女', '计算机4241班', 'Java程序设计', 87.50, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (57, '2024140512', '林娜', '女', '计算机4241班', 'C++程序设计', 84.00, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (58, '2024140512', '林娜', '女', '计算机4241班', '数据结构与算法', 90.00, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (59, '2024140512', '林娜', '女', '计算机4241班', '数据库原理', 86.50, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (60, '2024140512', '林娜', '女', '计算机4241班', '操作系统原理', 88.50, '计算机科学与技术', NULL, NULL);

INSERT INTO `score` VALUES (61, '2024140513', '何伟', '男', '计算机4241班', 'Java程序设计', 75.50, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (62, '2024140513', '何伟', '男', '计算机4241班', 'C++程序设计', 72.00, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (63, '2024140513', '何伟', '男', '计算机4241班', '数据结构与算法', 79.50, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (64, '2024140513', '何伟', '男', '计算机4241班', '数据库原理', 76.00, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (65, '2024140513', '何伟', '男', '计算机4241班', '操作系统原理', 78.50, '计算机科学与技术', NULL, NULL);

INSERT INTO `score` VALUES (66, '2024140514', '郑霞', '女', '计算机4241班', 'Java程序设计', 89.00, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (67, '2024140514', '郑霞', '女', '计算机4241班', 'C++程序设计', 86.50, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (68, '2024140514', '郑霞', '女', '计算机4241班', '数据结构与算法', 92.00, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (69, '2024140514', '郑霞', '女', '计算机4241班', '数据库原理', 88.50, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (70, '2024140514', '郑霞', '女', '计算机4241班', '操作系统原理', 90.50, '计算机科学与技术', NULL, NULL);

INSERT INTO `score` VALUES (71, '2024140515', '高峰', '男', '计算机4241班', 'Java程序设计', 81.50, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (72, '2024140515', '高峰', '男', '计算机4241班', 'C++程序设计', 78.00, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (73, '2024140515', '高峰', '男', '计算机4241班', '数据结构与算法', 85.50, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (74, '2024140515', '高峰', '男', '计算机4241班', '数据库原理', 82.00, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (75, '2024140515', '高峰', '男', '计算机4241班', '操作系统原理', 84.00, '计算机科学与技术', NULL, NULL);

INSERT INTO `score` VALUES (76, '2024140516', '梁雪', '女', '计算机4241班', 'Java程序设计', 94.00, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (77, '2024140516', '梁雪', '女', '计算机4241班', 'C++程序设计', 91.50, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (78, '2024140516', '梁雪', '女', '计算机4241班', '数据结构与算法', 96.00, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (79, '2024140516', '梁雪', '女', '计算机4241班', '数据库原理', 93.50, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (80, '2024140516', '梁雪', '女', '计算机4241班', '操作系统原理', 95.00, '计算机科学与技术', NULL, NULL);

INSERT INTO `score` VALUES (81, '2024140517', '田华', '男', '计算机4241班', 'Java程序设计', 77.00, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (82, '2024140517', '田华', '男', '计算机4241班', 'C++程序设计', 74.50, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (83, '2024140517', '田华', '男', '计算机4241班', '数据结构与算法', 81.00, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (84, '2024140517', '田华', '男', '计算机4241班', '数据库原理', 78.50, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (85, '2024140517', '田华', '男', '计算机4241班', '操作系统原理', 80.00, '计算机科学与技术', NULL, NULL);

INSERT INTO `score` VALUES (86, '2024140518', '邓琳', '女', '计算机4241班', 'Java程序设计', 85.50, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (87, '2024140518', '邓琳', '女', '计算机4241班', 'C++程序设计', 82.00, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (88, '2024140518', '邓琳', '女', '计算机4241班', '数据结构与算法', 88.50, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (89, '2024140518', '邓琳', '女', '计算机4241班', '数据库原理', 84.50, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (90, '2024140518', '邓琳', '女', '计算机4241班', '操作系统原理', 87.00, '计算机科学与技术', NULL, NULL);

INSERT INTO `score` VALUES (91, '2024140519', '曹军', '男', '计算机4241班', 'Java程序设计', 83.00, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (92, '2024140519', '曹军', '男', '计算机4241班', 'C++程序设计', 80.50, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (93, '2024140519', '曹军', '男', '计算机4241班', '数据结构与算法', 86.50, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (94, '2024140519', '曹军', '男', '计算机4241班', '数据库原理', 83.50, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (95, '2024140519', '曹军', '男', '计算机4241班', '操作系统原理', 85.50, '计算机科学与技术', NULL, NULL);

INSERT INTO `score` VALUES (96, '2024140520', '薛梅', '女', '计算机4241班', 'Java程序设计', 90.50, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (97, '2024140520', '薛梅', '女', '计算机4241班', 'C++程序设计', 87.50, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (98, '2024140520', '薛梅', '女', '计算机4241班', '数据结构与算法', 93.00, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (99, '2024140520', '薛梅', '女', '计算机4241班', '数据库原理', 89.50, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (100, '2024140520', '薛梅', '女', '计算机4241班', '操作系统原理', 91.50, '计算机科学与技术', NULL, NULL);

INSERT INTO `score` VALUES (101, '2024140521', '范斌', '男', '计算机4241班', 'Java程序设计', 78.50, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (102, '2024140521', '范斌', '男', '计算机4241班', 'C++程序设计', 75.00, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (103, '2024140521', '范斌', '男', '计算机4241班', '数据结构与算法', 82.50, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (104, '2024140521', '范斌', '男', '计算机4241班', '数据库原理', 79.00, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (105, '2024140521', '范斌', '男', '计算机4241班', '操作系统原理', 81.50, '计算机科学与技术', NULL, NULL);

INSERT INTO `score` VALUES (106, '2024140522', '石玉', '女', '计算机4241班', 'Java程序设计', 86.50, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (107, '2024140522', '石玉', '女', '计算机4241班', 'C++程序设计', 83.00, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (108, '2024140522', '石玉', '女', '计算机4241班', '数据结构与算法', 89.50, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (109, '2024140522', '石玉', '女', '计算机4241班', '数据库原理', 85.50, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (110, '2024140522', '石玉', '女', '计算机4241班', '操作系统原理', 88.00, '计算机科学与技术', NULL, NULL);

INSERT INTO `score` VALUES (111, '2024140523', '罗刚', '男', '计算机4241班', 'Java程序设计', 74.50, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (112, '2024140523', '罗刚', '男', '计算机4241班', 'C++程序设计', 71.00, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (113, '2024140523', '罗刚', '男', '计算机4241班', '数据结构与算法', 78.50, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (114, '2024140523', '罗刚', '男', '计算机4241班', '数据库原理', 75.00, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (115, '2024140523', '罗刚', '男', '计算机4241班', '操作系统原理', 77.50, '计算机科学与技术', NULL, NULL);

INSERT INTO `score` VALUES (116, '2024140524', '毛红', '女', '计算机4241班', 'Java程序设计', 88.00, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (117, '2024140524', '毛红', '女', '计算机4241班', 'C++程序设计', 85.50, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (118, '2024140524', '毛红', '女', '计算机4241班', '数据结构与算法', 91.00, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (119, '2024140524', '毛红', '女', '计算机4241班', '数据库原理', 87.50, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (120, '2024140524', '毛红', '女', '计算机4241班', '操作系统原理', 89.50, '计算机科学与技术', NULL, NULL);

INSERT INTO `score` VALUES (121, '2024140525', '段龙', '男', '计算机4241班', 'Java程序设计', 80.00, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (122, '2024140525', '段龙', '男', '计算机4241班', 'C++程序设计', 77.50, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (123, '2024140525', '段龙', '男', '计算机4241班', '数据结构与算法', 84.00, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (124, '2024140525', '段龙', '男', '计算机4241班', '数据库原理', 81.00, '计算机科学与技术', NULL, NULL);
INSERT INTO `score` VALUES (125, '2024140525', '段龙', '男', '计算机4241班', '操作系统原理', 83.00, '计算机科学与技术', NULL, NULL);

-- 软件工程专业学生成绩（2024120526-2024120533）
INSERT INTO `score` VALUES (126, '2024120526', '韩冰', '女', '软件4242班', 'Java程序设计', 92.00, '软件工程', NULL, NULL);
INSERT INTO `score` VALUES (127, '2024120526', '韩冰', '女', '软件4242班', 'C++程序设计', 89.50, '软件工程', NULL, NULL);
INSERT INTO `score` VALUES (128, '2024120526', '韩冰', '女', '软件4242班', '数据结构与算法', 95.00, '软件工程', NULL, NULL);
INSERT INTO `score` VALUES (129, '2024120526', '韩冰', '女', '软件4242班', '软件工程导论', 91.50, '软件工程', NULL, NULL);
INSERT INTO `score` VALUES (130, '2024120526', '韩冰', '女', '软件4242班', '软件测试技术', 93.50, '软件工程', NULL, NULL);

INSERT INTO `score` VALUES (131, '2024120527', '蒋亮', '男', '软件4242班', 'Java程序设计', 85.50, '软件工程', NULL, NULL);
INSERT INTO `score` VALUES (132, '2024120527', '蒋亮', '男', '软件4242班', 'C++程序设计', 82.00, '软件工程', NULL, NULL);
INSERT INTO `score` VALUES (133, '2024120527', '蒋亮', '男', '软件4242班', '数据结构与算法', 88.50, '软件工程', NULL, NULL);
INSERT INTO `score` VALUES (134, '2024120527', '蒋亮', '男', '软件4242班', '软件工程导论', 84.50, '软件工程', NULL, NULL);
INSERT INTO `score` VALUES (135, '2024120527', '蒋亮', '男', '软件4242班', '软件测试技术', 87.00, '软件工程', NULL, NULL);

INSERT INTO `score` VALUES (136, '2024120528', '谭燕', '女', '软件4242班', 'Java程序设计', 89.00, '软件工程', NULL, NULL);
INSERT INTO `score` VALUES (137, '2024120528', '谭燕', '女', '软件4242班', 'C++程序设计', 86.50, '软件工程', NULL, NULL);
INSERT INTO `score` VALUES (138, '2024120528', '谭燕', '女', '软件4242班', '数据结构与算法', 92.00, '软件工程', NULL, NULL);
INSERT INTO `score` VALUES (139, '2024120528', '谭燕', '女', '软件4242班', '软件工程导论', 88.50, '软件工程', NULL, NULL);
INSERT INTO `score` VALUES (140, '2024120528', '谭燕', '女', '软件4242班', '软件测试技术', 90.50, '软件工程', NULL, NULL);

INSERT INTO `score` VALUES (141, '2024120529', '廖勇', '男', '软件4242班', 'Java程序设计', 81.00, '软件工程', NULL, NULL);
INSERT INTO `score` VALUES (142, '2024120529', '廖勇', '男', '软件4242班', 'C++程序设计', 78.50, '软件工程', NULL, NULL);
INSERT INTO `score` VALUES (143, '2024120529', '廖勇', '男', '软件4242班', '数据结构与算法', 84.50, '软件工程', NULL, NULL);
INSERT INTO `score` VALUES (144, '2024120529', '廖勇', '男', '软件4242班', '软件工程导论', 80.50, '软件工程', NULL, NULL);
INSERT INTO `score` VALUES (145, '2024120529', '廖勇', '男', '软件4242班', '软件测试技术', 83.00, '软件工程', NULL, NULL);

INSERT INTO `score` VALUES (146, '2024120530', '姚丹', '女', '软件4242班', 'Java程序设计', 87.50, '软件工程', NULL, NULL);
INSERT INTO `score` VALUES (147, '2024120530', '姚丹', '女', '软件4242班', 'C++程序设计', 84.00, '软件工程', NULL, NULL);
INSERT INTO `score` VALUES (148, '2024120530', '姚丹', '女', '软件4242班', '数据结构与算法', 90.00, '软件工程', NULL, NULL);
INSERT INTO `score` VALUES (149, '2024120530', '姚丹', '女', '软件4242班', '软件工程导论', 86.50, '软件工程', NULL, NULL);
INSERT INTO `score` VALUES (150, '2024120530', '姚丹', '女', '软件4242班', '软件测试技术', 88.50, '软件工程', NULL, NULL);

INSERT INTO `score` VALUES (151, '2024120531', '袁超', '男', '软件4242班', 'Java程序设计', 76.50, '软件工程', NULL, NULL);
INSERT INTO `score` VALUES (152, '2024120531', '袁超', '男', '软件4242班', 'C++程序设计', 73.00, '软件工程', NULL, NULL);
INSERT INTO `score` VALUES (153, '2024120531', '袁超', '男', '软件4242班', '数据结构与算法', 80.00, '软件工程', NULL, NULL);
INSERT INTO `score` VALUES (154, '2024120531', '袁超', '男', '软件4242班', '软件工程导论', 77.50, '软件工程', NULL, NULL);
INSERT INTO `score` VALUES (155, '2024120531', '袁超', '男', '软件4242班', '软件测试技术', 79.00, '软件工程', NULL, NULL);

INSERT INTO `score` VALUES (156, '2024120532', '覃莉', '女', '软件4242班', 'Java程序设计', 93.50, '软件工程', NULL, NULL);
INSERT INTO `score` VALUES (157, '2024120532', '覃莉', '女', '软件4242班', 'C++程序设计', 90.00, '软件工程', NULL, NULL);
INSERT INTO `score` VALUES (158, '2024120532', '覃莉', '女', '软件4242班', '数据结构与算法', 96.00, '软件工程', NULL, NULL);
INSERT INTO `score` VALUES (159, '2024120532', '覃莉', '女', '软件4242班', '软件工程导论', 92.50, '软件工程', NULL, NULL);
INSERT INTO `score` VALUES (160, '2024120532', '覃莉', '女', '软件4242班', '软件测试技术', 94.50, '软件工程', NULL, NULL);

INSERT INTO `score` VALUES (161, '2024120533', '黎明', '男', '软件4242班', 'Java程序设计', 82.50, '软件工程', NULL, NULL);
INSERT INTO `score` VALUES (162, '2024120533', '黎明', '男', '软件4242班', 'C++程序设计', 79.00, '软件工程', NULL, NULL);
INSERT INTO `score` VALUES (163, '2024120533', '黎明', '男', '软件4242班', '数据结构与算法', 85.50, '软件工程', NULL, NULL);
INSERT INTO `score` VALUES (164, '2024120533', '黎明', '男', '软件4242班', '软件工程导论', 81.50, '软件工程', NULL, NULL);
INSERT INTO `score` VALUES (165, '2024120533', '黎明', '男', '软件4242班', '软件测试技术', 84.00, '软件工程', NULL, NULL);

-- 信息管理与信息系统专业学生成绩（2024110534-2024110542）
INSERT INTO `score` VALUES (166, '2024110534', '温静', '女', '信管4243班', 'Java程序设计', 86.00, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (167, '2024110534', '温静', '女', '信管4243班', '数据库原理', 89.50, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (168, '2024110534', '温静', '女', '信管4243班', '信息系统分析与设计', 91.00, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (169, '2024110534', '温静', '女', '信管4243班', '管理信息系统', 87.50, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (170, '2024110534', '温静', '女', '信管4243班', '信息检索', 88.50, '信息管理与信息系统', NULL, NULL);

INSERT INTO `score` VALUES (171, '2024110535', '苏波', '男', '信管4243班', 'Java程序设计', 79.50, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (172, '2024110535', '苏波', '男', '信管4243班', '数据库原理', 82.00, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (173, '2024110535', '苏波', '男', '信管4243班', '信息系统分析与设计', 84.50, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (174, '2024110535', '苏波', '男', '信管4243班', '管理信息系统', 80.50, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (175, '2024110535', '苏波', '男', '信管4243班', '信息检索', 81.50, '信息管理与信息系统', NULL, NULL);

INSERT INTO `score` VALUES (176, '2024110536', '顾欣', '女', '信管4243班', 'Java程序设计', 90.50, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (177, '2024110536', '顾欣', '女', '信管4243班', '数据库原理', 93.00, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (178, '2024110536', '顾欣', '女', '信管4243班', '信息系统分析与设计', 94.50, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (179, '2024110536', '顾欣', '女', '信管4243班', '管理信息系统', 91.50, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (180, '2024110536', '顾欣', '女', '信管4243班', '信息检索', 92.50, '信息管理与信息系统', NULL, NULL);

INSERT INTO `score` VALUES (181, '2024110537', '孔磊', '男', '信管4243班', 'Java程序设计', 75.00, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (182, '2024110537', '孔磊', '男', '信管4243班', '数据库原理', 78.50, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (183, '2024110537', '孔磊', '男', '信管4243班', '信息系统分析与设计', 80.00, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (184, '2024110537', '孔磊', '男', '信管4243班', '管理信息系统', 76.50, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (185, '2024110537', '孔磊', '男', '信管4243班', '信息检索', 77.50, '信息管理与信息系统', NULL, NULL);

INSERT INTO `score` VALUES (186, '2024110538', '严芳', '女', '信管4243班', 'Java程序设计', 88.00, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (187, '2024110538', '严芳', '女', '信管4243班', '数据库原理', 90.50, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (188, '2024110538', '严芳', '女', '信管4243班', '信息系统分析与设计', 92.00, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (189, '2024110538', '严芳', '女', '信管4243班', '管理信息系统', 89.00, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (190, '2024110538', '严芳', '女', '信管4243班', '信息检索', 90.00, '信息管理与信息系统', NULL, NULL);

INSERT INTO `score` VALUES (191, '2024110539', '武涛', '男', '信管4243班', 'Java程序设计', 83.50, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (192, '2024110539', '武涛', '男', '信管4243班', '数据库原理', 86.00, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (193, '2024110539', '武涛', '男', '信管4243班', '信息系统分析与设计', 87.50, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (194, '2024110539', '武涛', '男', '信管4243班', '管理信息系统', 84.50, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (195, '2024110539', '武涛', '男', '信管4243班', '信息检索', 85.50, '信息管理与信息系统', NULL, NULL);

INSERT INTO `score` VALUES (196, '2024110540', '钟娟', '女', '信管4243班', 'Java程序设计', 91.50, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (197, '2024110540', '钟娟', '女', '信管4243班', '数据库原理', 94.00, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (198, '2024110540', '钟娟', '女', '信管4243班', '信息系统分析与设计', 95.50, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (199, '2024110540', '钟娟', '女', '信管4243班', '管理信息系统', 92.50, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (200, '2024110540', '钟娟', '女', '信管4243班', '信息检索', 93.50, '信息管理与信息系统', NULL, NULL);

INSERT INTO `score` VALUES (201, '2024110541', '汤强', '男', '信管4243班', 'Java程序设计', 77.50, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (202, '2024110541', '汤强', '男', '信管4243班', '数据库原理', 80.00, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (203, '2024110541', '汤强', '男', '信管4243班', '信息系统分析与设计', 82.50, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (204, '2024110541', '汤强', '男', '信管4243班', '管理信息系统', 78.50, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (205, '2024110541', '汤强', '男', '信管4243班', '信息检索', 79.50, '信息管理与信息系统', NULL, NULL);

INSERT INTO `score` VALUES (206, '2024110542', '贺玲', '女', '信管4243班', 'Java程序设计', 85.00, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (207, '2024110542', '贺玲', '女', '信管4243班', '数据库原理', 87.50, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (208, '2024110542', '贺玲', '女', '信管4243班', '信息系统分析与设计', 89.00, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (209, '2024110542', '贺玲', '女', '信管4243班', '管理信息系统', 86.00, '信息管理与信息系统', NULL, NULL);
INSERT INTO `score` VALUES (210, '2024110542', '贺玲', '女', '信管4243班', '信息检索', 87.00, '信息管理与信息系统', NULL, NULL);

-- ----------------------------
-- Table structure for student
-- ----------------------------
DROP TABLE IF EXISTS `student`;
CREATE TABLE `student`  (
  `stu_id` int NOT NULL AUTO_INCREMENT COMMENT '编号',
  `stu_num` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '账号',
  `stu_name` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '姓名',
  `stu_sex` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '性别',
  `stu_age` int NOT NULL COMMENT '年龄',
  `stu_class` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '班级',
  `major` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '专业',
  `department` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '院系',
  PRIMARY KEY (`stu_id`) USING BTREE,
  UNIQUE INDEX `stu_num`(`stu_num` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 41 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Records of student (42个学生，与user表中的学生用户对应)
-- ----------------------------
-- 计算机科学与技术专业（140开头）
INSERT INTO `student` VALUES (1, '2024140501', '马浩', '男', 20, '计算机4241班', '计算机科学与技术', '计算机工程学院');
INSERT INTO `student` VALUES (2, '2024140502', '李雨', '女', 20, '计算机4241班', '计算机科学与技术', '计算机工程学院');
INSERT INTO `student` VALUES (3, '2024140503', '张远', '男', 20, '计算机4241班', '计算机科学与技术', '计算机工程学院');
INSERT INTO `student` VALUES (4, '2024140504', '王梅', '女', 20, '计算机4241班', '计算机科学与技术', '计算机工程学院');
INSERT INTO `student` VALUES (5, '2024140505', '陈杰', '男', 20, '计算机4241班', '计算机科学与技术', '计算机工程学院');
INSERT INTO `student` VALUES (6, '2024140506', '刘玲', '女', 20, '计算机4241班', '计算机科学与技术', '计算机工程学院');
INSERT INTO `student` VALUES (7, '2024140507', '杨博', '男', 20, '计算机4241班', '计算机科学与技术', '计算机工程学院');
INSERT INTO `student` VALUES (8, '2024140508', '赵燕', '女', 20, '计算机4241班', '计算机科学与技术', '计算机工程学院');
INSERT INTO `student` VALUES (9, '2024140509', '孙明', '男', 20, '计算机4241班', '计算机科学与技术', '计算机工程学院');
INSERT INTO `student` VALUES (10, '2024140510', '周婷', '女', 20, '计算机4241班', '计算机科学与技术', '计算机工程学院');
INSERT INTO `student` VALUES (11, '2024140511', '吴华', '男', 20, '计算机4241班', '计算机科学与技术', '计算机工程学院');
INSERT INTO `student` VALUES (12, '2024140512', '郑娜', '女', 20, '计算机4241班', '计算机科学与技术', '计算机工程学院');
INSERT INTO `student` VALUES (13, '2024140513', '冯宇', '男', 20, '计算机4241班', '计算机科学与技术', '计算机工程学院');
INSERT INTO `student` VALUES (14, '2024140514', '何敏', '女', 20, '计算机4241班', '计算机科学与技术', '计算机工程学院');
INSERT INTO `student` VALUES (15, '2024140515', '许强', '男', 20, '计算机4241班', '计算机科学与技术', '计算机工程学院');
INSERT INTO `student` VALUES (16, '2024140516', '蒋华', '女', 20, '计算机4241班', '计算机科学与技术', '计算机工程学院');
INSERT INTO `student` VALUES (17, '2024140517', '韩杰', '男', 20, '计算机4241班', '计算机科学与技术', '计算机工程学院');
INSERT INTO `student` VALUES (18, '2024140518', '曹芳', '女', 20, '计算机4241班', '计算机科学与技术', '计算机工程学院');
INSERT INTO `student` VALUES (19, '2024140519', '邓远', '男', 20, '计算机4241班', '计算机科学与技术', '计算机工程学院');
INSERT INTO `student` VALUES (20, '2024140521', '谢丽', '女', 20, '计算机4241班', '计算机科学与技术', '计算机工程学院');
INSERT INTO `student` VALUES (21, '2024140522', '罗国', '男', 20, '计算机4241班', '计算机科学与技术', '计算机工程学院');
INSERT INTO `student` VALUES (22, '2024140523', '梁玲', '女', 20, '计算机4241班', '计算机科学与技术', '计算机工程学院');
INSERT INTO `student` VALUES (23, '2024140524', '宋华', '男', 20, '计算机4241班', '计算机科学与技术', '计算机工程学院');
INSERT INTO `student` VALUES (24, '2024140525', '唐琴', '女', 20, '计算机4241班', '计算机科学与技术', '计算机工程学院');

-- 软件工程专业（141开头）
INSERT INTO `student` VALUES (25, '2024141001', '高博', '男', 20, '软件4241班', '软件工程', '计算机工程学院');
INSERT INTO `student` VALUES (26, '2024141002', '林燕', '女', 20, '软件4241班', '软件工程', '计算机工程学院');
INSERT INTO `student` VALUES (27, '2024141003', '胡明', '男', 20, '软件4241班', '软件工程', '计算机工程学院');
INSERT INTO `student` VALUES (28, '2024141004', '钟婷', '女', 20, '软件4241班', '软件工程', '计算机工程学院');
INSERT INTO `student` VALUES (29, '2024141005', '叶华', '男', 20, '软件4241班', '软件工程', '计算机工程学院');
INSERT INTO `student` VALUES (30, '2024141006', '苏娜', '女', 20, '软件4241班', '软件工程', '计算机工程学院');
INSERT INTO `student` VALUES (31, '2024141007', '潘宇', '男', 20, '软件4241班', '软件工程', '计算机工程学院');
INSERT INTO `student` VALUES (32, '2024141008', '董敏', '女', 20, '软件4241班', '软件工程', '计算机工程学院');
INSERT INTO `student` VALUES (33, '2024141009', '薛强', '男', 20, '软件4241班', '软件工程', '计算机工程学院');
INSERT INTO `student` VALUES (34, '2024141010', '范华', '女', 20, '软件4241班', '软件工程', '计算机工程学院');

-- 信息管理与信息系统专业（142开头）
INSERT INTO `student` VALUES (35, '2024142001', '石杰', '男', 20, '信管4241班', '信息管理与信息系统', '计算机工程学院');
INSERT INTO `student` VALUES (36, '2024142002', '姚芳', '女', 20, '信管4241班', '信息管理与信息系统', '计算机工程学院');
INSERT INTO `student` VALUES (37, '2024142003', '汪远', '男', 20, '信管4241班', '信息管理与信息系统', '计算机工程学院');
INSERT INTO `student` VALUES (38, '2024142004', '金丽', '女', 20, '信管4241班', '信息管理与信息系统', '计算机工程学院');
INSERT INTO `student` VALUES (39, '2024142005', '段国', '男', 20, '信管4241班', '信息管理与信息系统', '计算机工程学院');
INSERT INTO `student` VALUES (40, '2024142006', '白玲', '女', 20, '信管4241班', '信息管理与信息系统', '计算机工程学院');
INSERT INTO `student` VALUES (41, '2024142007', '康华', '男', 20, '信管4241班', '信息管理与信息系统', '计算机工程学院');
INSERT INTO `student` VALUES (42, '2024142008', '毛琴', '女', 20, '信管4241班', '信息管理与信息系统', '计算机工程学院');

-- ----------------------------
-- Table structure for teacher
-- ----------------------------
DROP TABLE IF EXISTS `teacher`;
CREATE TABLE `teacher`  (
  `tea_id` int NOT NULL AUTO_INCREMENT COMMENT '编号',
  `tea_num` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '工号',
  `tea_name` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '姓名',
  `tea_sex` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '性别',
  `tea_age` int NOT NULL COMMENT '年龄',
  `tea_course` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '所任课程',
  `major` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '专业',
  `department` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '院系',
  PRIMARY KEY (`tea_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Records of teacher (7个教师，与user表中的教师用户对应)
-- ----------------------------
INSERT INTO `teacher` VALUES (1, '2024000001', '张明', '男', 35, 'Java程序设计', '计算机科学与技术', '计算机工程学院');
INSERT INTO `teacher` VALUES (2, '2024000002', '李芳', '女', 32, 'C++程序设计', '计算机科学与技术', '计算机工程学院');
INSERT INTO `teacher` VALUES (3, '2024000003', '王建', '男', 38, '数据结构与算法', '计算机科学与技术', '计算机工程学院');
INSERT INTO `teacher` VALUES (4, '2024000004', '陈美', '女', 30, '数据库原理', '信息管理与信息系统', '计算机工程学院');
INSERT INTO `teacher` VALUES (5, '2024000005', '刘强', '男', 42, '操作系统原理', '计算机科学与技术', '计算机工程学院');
INSERT INTO `teacher` VALUES (6, '2024000006', '赵琴', '女', 36, '软件工程', '软件工程', '计算机工程学院');
INSERT INTO `teacher` VALUES (7, '2024000007', '孙华', '男', 40, '计算机网络', '计算机科学与技术', '计算机工程学院');

-- ----------------------------
-- Table structure for user
-- ----------------------------
DROP TABLE IF EXISTS `user`;
CREATE TABLE `user`  (
  `user_id` int NOT NULL AUTO_INCREMENT COMMENT '用户编号',
  `user_num` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '用户账号',
  `user_name` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '用户名',
  `password` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '密码',
  `phone` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '用户电话',
  `role_id` int NOT NULL COMMENT '角色编号（外键）',
  PRIMARY KEY (`user_id`, `user_num`) USING BTREE,
  CONSTRAINT `chk_phone` CHECK (regexp_like(`phone`,_utf8mb3'^1[3-9][0-9]{9}$'))
) ENGINE = InnoDB AUTO_INCREMENT = 47 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Records of user (50个真实用户数据)
-- 学号格式：10位数字，密码与学号一致，真实姓名和手机号
-- ----------------------------
-- 超级管理员
INSERT INTO `user` VALUES (1, '2024010000', '管理员', '2024010000', '13800138000', 2);

-- 教师账号（7个）
INSERT INTO `user` VALUES (2, '2024000001', '张明', '2024000001', '13912345678', 1);
INSERT INTO `user` VALUES (3, '2024000002', '李芳', '2024000002', '13823456789', 1);
INSERT INTO `user` VALUES (4, '2024000003', '王建', '2024000003', '13734567890', 1);
INSERT INTO `user` VALUES (5, '2024000004', '陈美', '2024000004', '13645678901', 1);
INSERT INTO `user` VALUES (6, '2024000005', '刘强', '2024000005', '13556789012', 1);
INSERT INTO `user` VALUES (7, '2024000006', '赵琴', '2024000006', '13467890123', 1);
INSERT INTO `user` VALUES (8, '2024000007', '孙华', '2024000007', '13378901234', 1);

-- 学生账号（42个）
-- 计算机科学与技术专业（140开头）
INSERT INTO `user` VALUES (9, '2024140501', '马浩', '2024140501', '13289012345', 0);
INSERT INTO `user` VALUES (10, '2024140502', '李雨', '2024140502', '13190123456', 0);
INSERT INTO `user` VALUES (11, '2024140503', '张远', '2024140503', '13801234567', 0);
INSERT INTO `user` VALUES (12, '2024140504', '王梅', '2024140504', '13712345678', 0);
INSERT INTO `user` VALUES (13, '2024140505', '陈杰', '2024140505', '13623456789', 0);
INSERT INTO `user` VALUES (14, '2024140506', '刘玲', '2024140506', '13534567890', 0);
INSERT INTO `user` VALUES (15, '2024140507', '杨博', '2024140507', '13445678901', 0);
INSERT INTO `user` VALUES (16, '2024140508', '赵燕', '2024140508', '13356789012', 0);
INSERT INTO `user` VALUES (17, '2024140509', '孙明', '2024140509', '13267890123', 0);
INSERT INTO `user` VALUES (18, '2024140510', '周婷', '2024140510', '13178901234', 0);
INSERT INTO `user` VALUES (19, '2024140511', '吴华', '2024140511', '13089012345', 0);
INSERT INTO `user` VALUES (20, '2024140512', '郑娜', '2024140512', '13990123456', 0);
INSERT INTO `user` VALUES (21, '2024140513', '冯宇', '2024140513', '13891234567', 0);
INSERT INTO `user` VALUES (22, '2024140514', '何敏', '2024140514', '13792345678', 0);
INSERT INTO `user` VALUES (23, '2024140515', '许强', '2024140515', '13693456789', 0);
INSERT INTO `user` VALUES (24, '2024140516', '蒋华', '2024140516', '13594567890', 0);
INSERT INTO `user` VALUES (25, '2024140517', '韩杰', '2024140517', '13495678901', 0);
INSERT INTO `user` VALUES (26, '2024140518', '曹芳', '2024140518', '13396789012', 0);
INSERT INTO `user` VALUES (27, '2024140519', '邓远', '2024140519', '13297890123', 0);
INSERT INTO `user` VALUES (28, '2024140521', '谢丽', '2024140521', '13198901234', 0);
INSERT INTO `user` VALUES (29, '2024140522', '罗国', '2024140522', '13099012345', 0);
INSERT INTO `user` VALUES (30, '2024140523', '梁玲', '2024140523', '13900123456', 0);
INSERT INTO `user` VALUES (31, '2024140524', '宋华', '2024140524', '13801234568', 0);
INSERT INTO `user` VALUES (32, '2024140525', '唐琴', '2024140525', '13702345679', 0);

-- 软件工程专业（141开头）
INSERT INTO `user` VALUES (33, '2024141001', '高博', '2024141001', '13603456780', 0);
INSERT INTO `user` VALUES (34, '2024141002', '林燕', '2024141002', '13504567891', 0);
INSERT INTO `user` VALUES (35, '2024141003', '胡明', '2024141003', '13405678902', 0);
INSERT INTO `user` VALUES (36, '2024141004', '钟婷', '2024141004', '13306789013', 0);
INSERT INTO `user` VALUES (37, '2024141005', '叶华', '2024141005', '13207890124', 0);
INSERT INTO `user` VALUES (38, '2024141006', '苏娜', '2024141006', '13108901235', 0);
INSERT INTO `user` VALUES (39, '2024141007', '潘宇', '2024141007', '13009012346', 0);
INSERT INTO `user` VALUES (40, '2024141008', '董敏', '2024141008', '13910123457', 0);
INSERT INTO `user` VALUES (41, '2024141009', '薛强', '2024141009', '13811234568', 0);
INSERT INTO `user` VALUES (42, '2024141010', '范华', '2024141010', '13712345679', 0);

-- 信息管理与信息系统专业（142开头）
INSERT INTO `user` VALUES (43, '2024142001', '石杰', '2024142001', '13613456780', 0);
INSERT INTO `user` VALUES (44, '2024142002', '姚芳', '2024142002', '13514567891', 0);
INSERT INTO `user` VALUES (45, '2024142003', '汪远', '2024142003', '13415678902', 0);
INSERT INTO `user` VALUES (46, '2024142004', '金丽', '2024142004', '13316789013', 0);
INSERT INTO `user` VALUES (47, '2024142005', '段国', '2024142005', '13217890124', 0);
INSERT INTO `user` VALUES (48, '2024142006', '白玲', '2024142006', '13118901235', 0);
INSERT INTO `user` VALUES (49, '2024142007', '康华', '2024142007', '13019012346', 0);
INSERT INTO `user` VALUES (50, '2024142008', '毛琴', '2024142008', '13920123457', 0);

-- ----------------------------
-- Triggers structure for table student
-- ----------------------------
DROP TRIGGER IF EXISTS `stuLogin`;
delimiter ;;
CREATE TRIGGER `stuLogin` AFTER DELETE ON `student` FOR EACH ROW begin 
delete from user where user_num=old.stu_num;
end
;;
delimiter ;

-- ----------------------------
-- Triggers structure for table teacher
-- ----------------------------
DROP TRIGGER IF EXISTS `teaLogin`;
delimiter ;;
CREATE TRIGGER `teaLogin` AFTER DELETE ON `teacher` FOR EACH ROW begin
delete from user where user_num=old.tea_num;
end
;;
delimiter ;

SET FOREIGN_KEY_CHECKS = 1;
