/*
 * Generated by the Jasper component of Apache Tomcat
 * Version: Apache Tomcat/7.0.47
 * Generated at: 2025-06-20 06:19:42 UTC
 * Note: The last modified time of this file was set to
 *       the last modified time of the source file after
 *       generation to assist with modification tracking.
 */
package org.apache.jsp.admin.score;

import javax.servlet.*;
import javax.servlet.http.*;
import javax.servlet.jsp.*;
import com.ntvu.studentmis.db.DBScore;
import com.ntvu.studentmis.entity.Score;
import java.io.*;
import com.ntvu.studentmis.entity.User;
import com.ntvu.studentmis.db.DBManager;

public final class edit_jsp extends org.apache.jasper.runtime.HttpJspBase
    implements org.apache.jasper.runtime.JspSourceDependent {

  private static final javax.servlet.jsp.JspFactory _jspxFactory =
          javax.servlet.jsp.JspFactory.getDefaultFactory();

  private static java.util.Map<java.lang.String,java.lang.Long> _jspx_dependants;

  static {
    _jspx_dependants = new java.util.HashMap<java.lang.String,java.lang.Long>(4);
    _jspx_dependants.put("/admin/score/../../include/left_menu.jsp", Long.valueOf(1750296532725L));
    _jspx_dependants.put("/admin/score/../../include/header_nav.jsp", Long.valueOf(1750250240204L));
    _jspx_dependants.put("/admin/score/../../include/../include/menu.jsp", Long.valueOf(1672200865841L));
    _jspx_dependants.put("/admin/score/../../include/foot_js.jsp", Long.valueOf(1685610773249L));
  }

  private javax.el.ExpressionFactory _el_expressionfactory;
  private org.apache.tomcat.InstanceManager _jsp_instancemanager;

  public java.util.Map<java.lang.String,java.lang.Long> getDependants() {
    return _jspx_dependants;
  }

  public void _jspInit() {
    _el_expressionfactory = _jspxFactory.getJspApplicationContext(getServletConfig().getServletContext()).getExpressionFactory();
    _jsp_instancemanager = org.apache.jasper.runtime.InstanceManagerFactory.getInstanceManager(getServletConfig());
  }

  public void _jspDestroy() {
  }

  public void _jspService(final javax.servlet.http.HttpServletRequest request, final javax.servlet.http.HttpServletResponse response)
        throws java.io.IOException, javax.servlet.ServletException {

    final javax.servlet.jsp.PageContext pageContext;
    javax.servlet.http.HttpSession session = null;
    final javax.servlet.ServletContext application;
    final javax.servlet.ServletConfig config;
    javax.servlet.jsp.JspWriter out = null;
    final java.lang.Object page = this;
    javax.servlet.jsp.JspWriter _jspx_out = null;
    javax.servlet.jsp.PageContext _jspx_page_context = null;


    try {
      response.setContentType("text/html; charset=UTF-8");
      pageContext = _jspxFactory.getPageContext(this, request, response,
      			null, true, 8192, true);
      _jspx_page_context = pageContext;
      application = pageContext.getServletContext();
      config = pageContext.getServletConfig();
      session = pageContext.getSession();
      out = pageContext.getOut();
      _jspx_out = out;

      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("<!DOCTYPE html>\r\n");
      out.write("<html lang=\"en\">\r\n");
      out.write("<head>\r\n");
      out.write("    <meta charset=\"utf-8\">\r\n");
      out.write("    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\">\r\n");
      out.write("    ");
      org.apache.jasper.runtime.JspRuntimeLibrary.include(request, response, "../../include/header_css.jsp", out, true);
      out.write("\r\n");
      out.write("    <title>修改学生成绩</title>\r\n");
      out.write("</head>\r\n");
      out.write("<body class=\"hold-transition sidebar-mini layout-fixed\">\r\n");
      out.write("<div class=\"wrapper\">\r\n");
      out.write("    ");
      out.write('\r');
      out.write('\n');
      out.write("\r\n");
      out.write("\r\n");
      out.write('\r');
      out.write('\n');

    //用户没登录则不允许访问登录页面
    if(session.getAttribute("curUserName")==null)
    {
        response.sendRedirect("../../login.jsp");
        return;
    }
    //取得当前环境名称 /javaweb
    String contextPath=request.getContextPath();

      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("<!-- Preloader -->\r\n");
      out.write('\r');
      out.write('\n');
      out.write('\r');
      out.write('\n');
      out.write('\r');
      out.write('\n');

    String curUserName=(String)session.getAttribute("curUserName");

      out.write("\r\n");
      out.write("<!-- Navbar -->\r\n");
      out.write("<nav class=\"main-header navbar navbar-expand navbar-white navbar-light\">\r\n");
      out.write("    <!-- Left navbar links -->\r\n");
      out.write("    <ul class=\"navbar-nav\">\r\n");
      out.write("        <li class=\"nav-item\">\r\n");
      out.write("            <a class=\"nav-link\" data-widget=\"pushmenu\" href=\"#\" role=\"button\"><i class=\"fas fa-bars\"></i></a>\r\n");
      out.write("        </li>\r\n");
      out.write("        <li class=\"nav-item d-none d-sm-inline-block\">\r\n");
      out.write("            <a href=\"");
      out.print( request.getContextPath() + "/admin/user/list.jsp");
      out.write("\" class=\"nav-link\" style=\"color: blue\">主页</a>\r\n");
      out.write("        </li>\r\n");
      out.write("\r\n");
      out.write("        <li class=\"nav-item d-none d-sm-inline-block\">\r\n");
      out.write("            <a href=\"");
      out.print( contextPath );
      out.write("/LoginOut\" class=\"nav-link\" style=\"color: #ff0000\">退出</a>\r\n");
      out.write("        </li>\r\n");
      out.write("    </ul>\r\n");
      out.write("\r\n");
      out.write("    <!-- Right navbar links -->\r\n");
      out.write("    <ul class=\"navbar-nav ml-auto\">\r\n");
      out.write("        <!-- Navbar Search -->\r\n");
      out.write("        <li class=\"nav-item\">\r\n");
      out.write("            <a class=\"nav-link\" data-widget=\"navbar-search\" href=\"#\" role=\"button\">\r\n");
      out.write("                <i class=\"fas fa-search\"></i>\r\n");
      out.write("            </a>\r\n");
      out.write("            <div class=\"navbar-search-block\">\r\n");
      out.write("                <form class=\"form-inline\">\r\n");
      out.write("                    <div class=\"input-group input-group-sm\">\r\n");
      out.write("                        <input class=\"form-control form-control-navbar\" type=\"search\" placeholder=\"Search\" aria-label=\"Search\">\r\n");
      out.write("                        <div class=\"input-group-append\">\r\n");
      out.write("                            <button class=\"btn btn-navbar\" type=\"submit\">\r\n");
      out.write("                                <i class=\"fas fa-search\"></i>\r\n");
      out.write("                            </button>\r\n");
      out.write("                            <button class=\"btn btn-navbar\" type=\"button\" data-widget=\"navbar-search\">\r\n");
      out.write("                                <i class=\"fas fa-times\"></i>\r\n");
      out.write("                            </button>\r\n");
      out.write("                        </div>\r\n");
      out.write("                    </div>\r\n");
      out.write("                </form>\r\n");
      out.write("            </div>\r\n");
      out.write("        </li>\r\n");
      out.write("\r\n");
      out.write("        <li class=\"nav-item\">\r\n");
      out.write("            <a class=\"nav-link\" data-widget=\"fullscreen\" href=\"#\" role=\"button\">\r\n");
      out.write("                <i class=\"fas fa-expand-arrows-alt\"></i>\r\n");
      out.write("            </a>\r\n");
      out.write("        </li>\r\n");
      out.write("        <li class=\"nav-item\">\r\n");
      out.write("            <a class=\"nav-link\" data-widget=\"control-sidebar\" data-controlsidebar-slide=\"true\" href=\"#\" role=\"button\">\r\n");
      out.write("                <i class=\"fas fa-th-large\"></i>\r\n");
      out.write("            </a>\r\n");
      out.write("        </li>\r\n");
      out.write("    </ul>\r\n");
      out.write("</nav>\r\n");
      out.write("<!-- /.navbar -->");
      out.write("\r\n");
      out.write("    ");
      out.write('\r');
      out.write('\n');
      out.write("\r\n");
      out.write("\r\n");
      out.write("<!-- Main Sidebar Container -->\r\n");
      out.write("<aside class=\"main-sidebar sidebar-dark-primary elevation-4\">\r\n");
      out.write("    <!-- Brand Logo -->\r\n");
      out.write("    <!-- Sidebar -->\r\n");
      out.write("    <div class=\"sidebar\">\r\n");
      out.write("        <!-- Sidebar user panel (optional) -->\r\n");
      out.write("        <div class=\"user-panel mt-3 pb-3 mb-3 d-flex\">\r\n");
      out.write("            ");

                String login_name=(String)session.getAttribute("curUserName");
                User g_user=new DBManager().getDetails(login_name);
            
      out.write("\r\n");
      out.write("            <div class=\"image\">\r\n");
      out.write("                ");
      out.write("\r\n");
      out.write("                <img src=\"");
      out.print( request.getContextPath() + "/uploadPic/user3.jpg");
      out.write("\" class=\"img-circle elevation-2\" alt=\"User Image\">\r\n");
      out.write("            </div>\r\n");
      out.write("            <div class=\"info\">\r\n");
      out.write("                <a href=\"");
      out.print( request.getContextPath() + "/include/info.jsp");
      out.write("\" class=\"d-block\" style=\"color: white\">");
      out.print( session.getAttribute("curUserName"));
      out.write("</a>\r\n");
      out.write("            </div>\r\n");
      out.write("        </div>\r\n");
      out.write("\r\n");
      out.write("        <!-- Sidebar Menu -->\r\n");
      out.write("        <nav class=\"mt-2\">\r\n");
      out.write("            <ul class=\"nav nav-pills nav-sidebar flex-column\" data-widget=\"treeview\" role=\"menu\" data-accordion=\"false\">\r\n");
      out.write("                <!-- Add icons to the links using the .nav-icon class\r\n");
      out.write("                     with font-awesome or any other icon font library -->\r\n");
      out.write("                <li class=\"nav-item\">\r\n");
      out.write("                    <a href=\"#\" class=\"nav-link active\">\r\n");
      out.write("                        <i class=\"nav-icon fas fa-user\"></i>\r\n");
      out.write("                        <p>\r\n");
      out.write("                            用户管理\r\n");
      out.write("                            <i class=\"right fas fa-angle-left\"></i>\r\n");
      out.write("                        </p>\r\n");
      out.write("                    </a>\r\n");
      out.write("                    <ul class=\"nav nav-treeview\">\r\n");
      out.write("                        <li class=\"nav-item\">\r\n");
      out.write("                            <a href=\"");
      out.print( request.getContextPath() + "/admin/user/list.jsp");
      out.write("\" class=\"nav-link\">\r\n");
      out.write("                                <i class=\"far fa-circle nav-icon\"></i>\r\n");
      out.write("                                <p>用户列表</p>\r\n");
      out.write("                            </a>\r\n");
      out.write("                        </li>\r\n");
      out.write("                        <li class=\"nav-item\">\r\n");
      out.write("                            <a href=\"");
      out.print( request.getContextPath() + "/admin/user/add.jsp");
      out.write("\" class=\"nav-link\">\r\n");
      out.write("                                <i class=\"far fa-circle nav-icon\"></i>\r\n");
      out.write("                                <p>添加用户</p>\r\n");
      out.write("                            </a>\r\n");
      out.write("                        </li>\r\n");
      out.write("                    </ul>\r\n");
      out.write("                </li>\r\n");
      out.write("                <li class=\"nav-item\">\r\n");
      out.write("                    <a href=\"#\" class=\"nav-link active\">\r\n");
      out.write("                        <i class=\"nav-icon fas fa-user\"></i>\r\n");
      out.write("                        <p>\r\n");
      out.write("                            学生信息管理\r\n");
      out.write("                            <i class=\"right fas fa-angle-left\"></i>\r\n");
      out.write("                        </p>\r\n");
      out.write("                    </a>\r\n");
      out.write("                    <ul class=\"nav nav-treeview\">\r\n");
      out.write("                        <li class=\"nav-item\">\r\n");
      out.write("                            <a href=\"");
      out.print( request.getContextPath() + "/admin/student/list.jsp");
      out.write("\" class=\"nav-link\">\r\n");
      out.write("                                <i class=\"far fa-circle nav-icon\"></i>\r\n");
      out.write("                                <p>学生信息列表</p>\r\n");
      out.write("                            </a>\r\n");
      out.write("                        </li>\r\n");
      out.write("                        <li class=\"nav-item\">\r\n");
      out.write("                            <a href=\"");
      out.print( request.getContextPath() + "/admin/student/add.jsp");
      out.write("\" class=\"nav-link\">\r\n");
      out.write("                                <i class=\"far fa-circle nav-icon\"></i>\r\n");
      out.write("                                <p>添加学生信息</p>\r\n");
      out.write("                            </a>\r\n");
      out.write("                        </li>\r\n");
      out.write("                    </ul>\r\n");
      out.write("                </li>\r\n");
      out.write("                <li class=\"nav-item\">\r\n");
      out.write("                    <a href=\"#\" class=\"nav-link active\">\r\n");
      out.write("                        <i class=\"nav-icon fas fa-user\"></i>\r\n");
      out.write("                        <p>\r\n");
      out.write("                            课程信息管理\r\n");
      out.write("                            <i class=\"right fas fa-angle-left\"></i>\r\n");
      out.write("                        </p>\r\n");
      out.write("                    </a>\r\n");
      out.write("                    <ul class=\"nav nav-treeview\">\r\n");
      out.write("                        <li class=\"nav-item\">\r\n");
      out.write("                            <a href=\"");
      out.print( request.getContextPath() + "/admin/course/list.jsp");
      out.write("\" class=\"nav-link\">\r\n");
      out.write("                                <i class=\"far fa-circle nav-icon\"></i>\r\n");
      out.write("                                <p>课程信息列表</p>\r\n");
      out.write("                            </a>\r\n");
      out.write("                        </li>\r\n");
      out.write("                        <li class=\"nav-item\">\r\n");
      out.write("                            <a href=\"");
      out.print( request.getContextPath() + "/admin/course/add.jsp");
      out.write("\" class=\"nav-link\">\r\n");
      out.write("                                <i class=\"far fa-circle nav-icon\"></i>\r\n");
      out.write("                                <p>添加课程信息</p>\r\n");
      out.write("                            </a>\r\n");
      out.write("                        </li>\r\n");
      out.write("                    </ul>\r\n");
      out.write("                </li>\r\n");
      out.write("                <li class=\"nav-item\">\r\n");
      out.write("                    <a href=\"#\" class=\"nav-link active\">\r\n");
      out.write("                        <i class=\"nav-icon fas fa-user\"></i>\r\n");
      out.write("                        <p>\r\n");
      out.write("                            学生成绩管理\r\n");
      out.write("                            <i class=\"right fas fa-angle-left\"></i>\r\n");
      out.write("                        </p>\r\n");
      out.write("                    </a>\r\n");
      out.write("                    <ul class=\"nav nav-treeview\">\r\n");
      out.write("                        <li class=\"nav-item\">\r\n");
      out.write("                            <a href=\"");
      out.print( request.getContextPath() + "/admin/score/list.jsp");
      out.write("\" class=\"nav-link\">\r\n");
      out.write("                                <i class=\"far fa-circle nav-icon\"></i>\r\n");
      out.write("                                <p>学生成绩列表</p>\r\n");
      out.write("                            </a>\r\n");
      out.write("                        </li>\r\n");
      out.write("                        <li class=\"nav-item\">\r\n");
      out.write("                            <a href=\"");
      out.print( request.getContextPath() + "/admin/score/add.jsp");
      out.write("\" class=\"nav-link\">\r\n");
      out.write("                                <i class=\"far fa-circle nav-icon\"></i>\r\n");
      out.write("                                <p>添加学生成绩</p>\r\n");
      out.write("                            </a>\r\n");
      out.write("                        </li>\r\n");
      out.write("                        <li class=\"nav-item\">\r\n");
      out.write("                            <a href=\"");
      out.print( request.getContextPath() + "/admin/student/ToExcel");
      out.write("\" class=\"nav-link\">\r\n");
      out.write("                                <i class=\"far fa-circle nav-icon\"></i>\r\n");
      out.write("                                <p>学生成绩报表</p>\r\n");
      out.write("                            </a>\r\n");
      out.write("                        </li>\r\n");
      out.write("                    </ul>\r\n");
      out.write("                </li>\r\n");
      out.write("                <li class=\"nav-item\">\r\n");
      out.write("                    <a href=\"#\" class=\"nav-link active\">\r\n");
      out.write("                        <i class=\"nav-icon fas fa-user\"></i>\r\n");
      out.write("                        <p>\r\n");
      out.write("                           教师信息管理\r\n");
      out.write("                            <i class=\"right fas fa-angle-left\"></i>\r\n");
      out.write("                        </p>\r\n");
      out.write("                    </a>\r\n");
      out.write("                    <ul class=\"nav nav-treeview\">\r\n");
      out.write("                        <li class=\"nav-item\">\r\n");
      out.write("                            <a href=\"");
      out.print( request.getContextPath() + "/admin/teacher/list.jsp");
      out.write("\" class=\"nav-link\">\r\n");
      out.write("                                <i class=\"far fa-circle nav-icon\"></i>\r\n");
      out.write("                                <p> 教师信息列表</p>\r\n");
      out.write("                            </a>\r\n");
      out.write("                        </li>\r\n");
      out.write("                        <li class=\"nav-item\">\r\n");
      out.write("                            <a href=\"");
      out.print( request.getContextPath() + "/admin/teacher/add.jsp");
      out.write("\" class=\"nav-link\">\r\n");
      out.write("                                <i class=\"far fa-circle nav-icon\"></i>\r\n");
      out.write("                                <p>添加教师信息</p>\r\n");
      out.write("                            </a>\r\n");
      out.write("                        </li>\r\n");
      out.write("                    </ul>\r\n");
      out.write("                </li>\r\n");
      out.write("            </ul>\r\n");
      out.write("        </nav>\r\n");
      out.write("        <!-- /.sidebar-menu -->\r\n");
      out.write("    </div>\r\n");
      out.write("    <!-- /.sidebar -->\r\n");
      out.write("</aside>\r\n");
      out.write("\r\n");
      out.write("    <div class=\"content-wrapper\" style=\"min-height: 1345.6px;\">\r\n");
      out.write("        <!-- Content Header (Page header) -->\r\n");
      out.write("        <section class=\"content-header\">\r\n");
      out.write("            <div class=\"container-fluid\">\r\n");
      out.write("                <div class=\"row mb-2\">\r\n");
      out.write("                    <div class=\"col-sm-6\">\r\n");
      out.write("                        <h1>修改学生成绩</h1>\r\n");
      out.write("                    </div>\r\n");
      out.write("                </div>\r\n");
      out.write("            </div><!-- /.container-fluid -->\r\n");
      out.write("        </section>\r\n");
      out.write("\r\n");
      out.write("        ");

            String id = request.getParameter("id");
            // 调试信息：获取成绩ID参数
            Score score = null;
            if (id != null && !id.trim().isEmpty()) {
                try {
                    score = new DBScore().getListById(Integer.parseInt(id));
                } catch (NumberFormatException e) {
                    e.printStackTrace();
                    response.sendRedirect("list.jsp");
                    return;
                }
            }
            if (score == null) {
                response.sendRedirect("list.jsp");
                return;
            }
        
      out.write("\r\n");
      out.write("        <!-- Main content -->\r\n");
      out.write("        <section class=\"content\">\r\n");
      out.write("            <div class=\"container-fluid\">\r\n");
      out.write("                <div class=\"row\">\r\n");
      out.write("                    <!-- left column -->\r\n");
      out.write("                    <div class=\"col-md-12\">\r\n");
      out.write("                        <!-- jquery validation -->\r\n");
      out.write("                        <div class=\"card card-primary\">\r\n");
      out.write("                            <div class=\"card-header\">\r\n");
      out.write("                                <h3 class=\"card-title\">请修改<small>学生信息</small></h3>\r\n");
      out.write("                            </div>\r\n");
      out.write("                            <!-- /.card-header -->\r\n");
      out.write("                            <!-- form start -->\r\n");
      out.write("                            <form name=\"form1\" id=\"form1\" method=\"post\" action=\"");
      out.print( request.getContextPath() + "/admin/score/ScoreServlet?action=edit");
      out.write("\" onsubmit=\"return verify()\">\r\n");
      out.write("                                <input type=\"hidden\" name=\"id\" value=\"");
      out.print( score.getScore_id());
      out.write("\">\r\n");
      out.write("                                <div class=\"card-body\">\r\n");
      out.write("                                    <div class=\"form-group\">\r\n");
      out.write("                                        <label >学号</label>\r\n");
      out.write("                                        <input type=\"text\" name=\"txtNum\" class=\"form-control\" value=\"");
      out.print(score.getStu_num());
      out.write("\">\r\n");
      out.write("                                    </div>\r\n");
      out.write("                                    <div class=\"form-group\">\r\n");
      out.write("                                        <label >姓名</label>\r\n");
      out.write("                                        <input type=\"text\" name=\"txtName\" class=\"form-control\"  value=\"");
      out.print(score.getStu_name());
      out.write("\">\r\n");
      out.write("                                    </div>\r\n");
      out.write("                                    <div class=\"form-group\">\r\n");
      out.write("                                        <label >性别</label>\r\n");
      out.write("                                        <input type=\"text\" name=\"txtSex\" class=\"form-control\" value=\"");
      out.print(score.getStu_sex());
      out.write("\">\r\n");
      out.write("                                    </div>\r\n");
      out.write("                                    <div class=\"form-group\">\r\n");
      out.write("                                        <label>班级</label>\r\n");
      out.write("                                        <input type=\"text\" name=\"txtClass\" class=\"form-control\"  value=\"");
      out.print(score.getStu_class());
      out.write("\">\r\n");
      out.write("                                    </div>\r\n");
      out.write("                                    <div class=\"form-group\">\r\n");
      out.write("                                        <label>科目</label>\r\n");
      out.write("                                        <input type=\"text\" name=\"txtCourse\" class=\"form-control\"  value=\"");
      out.print(score.getCourse_name());
      out.write("\">\r\n");
      out.write("                                    </div>\r\n");
      out.write("                                    <div class=\"form-group\">\r\n");
      out.write("                                        <label>成绩</label>\r\n");
      out.write("                                        <input type=\"text\" name=\"txtScore\" class=\"form-control\"  value=\"");
      out.print(score.getScore_grade());
      out.write("\">\r\n");
      out.write("                                    </div>\r\n");
      out.write("                                    <div class=\"form-group\">\r\n");
      out.write("                                        <label >专业</label>\r\n");
      out.write("                                        <input type=\"text\" name=\"txtMajor\" class=\"form-control\" value=\"");
      out.print(score.getMajor());
      out.write("\">\r\n");
      out.write("                                    </div>\r\n");
      out.write("                                </div>\r\n");
      out.write("                                <!-- /.card-body -->\r\n");
      out.write("                                <div class=\"card-footer\">\r\n");
      out.write("                                    <input type=\"submit\" class=\"btn btn-primary\" name=\"btnSubmit\" value=\"提交\">\r\n");
      out.write("                                </div>\r\n");
      out.write("                            </form>\r\n");
      out.write("                        </div>\r\n");
      out.write("                        <!-- /.card -->\r\n");
      out.write("                    </div>\r\n");
      out.write("                    <!--/.col (left) -->\r\n");
      out.write("                    <!-- right column -->\r\n");
      out.write("                    <div class=\"col-md-6\">\r\n");
      out.write("\r\n");
      out.write("                    </div>\r\n");
      out.write("                    <!--/.col (right) -->\r\n");
      out.write("                </div>\r\n");
      out.write("                <!-- /.row -->\r\n");
      out.write("            </div><!-- /.container-fluid -->\r\n");
      out.write("        </section>\r\n");
      out.write("        <!-- /.content -->\r\n");
      out.write("    </div>\r\n");
      out.write("</div>\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("<!-- jQuery -->\r\n");
      out.write("<script src=\"");
      out.print( request.getContextPath());
      out.write("/plugins/jquery/jquery.min.js\"></script>\r\n");
      out.write("<!-- jQuery UI 1.11.4 -->\r\n");
      out.write("<script src=\"");
      out.print( request.getContextPath());
      out.write("/plugins/jquery-ui/jquery-ui.min.js\"></script>\r\n");
      out.write("<!-- Resolve conflict in jQuery UI tooltip with Bootstrap tooltip -->\r\n");
      out.write("<script>\r\n");
      out.write("    $.widget.bridge('uibutton', $.ui.button)\r\n");
      out.write("</script>\r\n");
      out.write("\r\n");
      out.write("<!-- Bootstrap 4 -->\r\n");
      out.write("<script src=\"");
      out.print( request.getContextPath());
      out.write("/plugins/bootstrap/js/bootstrap.bundle.min.js\"></script>\r\n");
      out.write("<!-- ChartJS -->\r\n");
      out.write("<script src=\"");
      out.print( request.getContextPath());
      out.write("/plugins/chart.js/Chart.min.js\"></script>\r\n");
      out.write("<!-- Sparkline -->\r\n");
      out.write("<script src=\"");
      out.print( request.getContextPath());
      out.write("/plugins/sparklines/sparkline.js\"></script>\r\n");
      out.write("<!-- JQVMap -->\r\n");
      out.write("<script src=\"");
      out.print( request.getContextPath());
      out.write("/plugins/jqvmap/jquery.vmap.min.js\"></script>\r\n");
      out.write("<script src=\"");
      out.print( request.getContextPath());
      out.write("/plugins/jqvmap/maps/jquery.vmap.usa.js\"></script>\r\n");
      out.write("<!-- jQuery Knob Chart -->\r\n");
      out.write("<script src=\"");
      out.print( request.getContextPath());
      out.write("/plugins/jquery-knob/jquery.knob.min.js\"></script>\r\n");
      out.write("<!-- daterangepicker -->\r\n");
      out.write("<script src=\"");
      out.print( request.getContextPath());
      out.write("/plugins/moment/moment.min.js\"></script>\r\n");
      out.write("<script src=\"");
      out.print( request.getContextPath());
      out.write("/plugins/daterangepicker/daterangepicker.js\"></script>\r\n");
      out.write("<!-- Tempusdominus Bootstrap 4 -->\r\n");
      out.write("<script src=\"");
      out.print( request.getContextPath());
      out.write("/plugins/tempusdominus-bootstrap-4/js/tempusdominus-bootstrap-4.min.js\"></script>\r\n");
      out.write("<!-- Summernote -->\r\n");
      out.write("<script src=\"");
      out.print( request.getContextPath());
      out.write("/plugins/summernote/summernote-bs4.min.js\"></script>\r\n");
      out.write("<!-- overlayScrollbars -->\r\n");
      out.write("<script src=\"");
      out.print( request.getContextPath());
      out.write("/plugins/overlayScrollbars/js/jquery.overlayScrollbars.min.js\"></script>\r\n");
      out.write("<!-- AdminLTE App -->\r\n");
      out.write("<script src=\"");
      out.print( request.getContextPath());
      out.write("/dist/js/adminlte.js\"></script>\r\n");
      out.write("<!-- AdminLTE for demo purposes -->\r\n");
      out.write("<script src=\"");
      out.print( request.getContextPath());
      out.write("/dist/js/demo.js\"></script>\r\n");
      out.write("<!-- AdminLTE dashboard demo (This is only for demo purposes) -->\r\n");
      out.write("<script src=\"");
      out.print( request.getContextPath());
      out.write("/dist/js/pages/dashboard.js\"></script>\r\n");
      out.write("<script type=\"text/javascript\" src=\"");
      out.print( request.getContextPath());
      out.write("/js/jquery-3.6.1.min.js\"></script>\r\n");
      out.write("<script type=\"text/javascript\" src=\"");
      out.print( request.getContextPath());
      out.write("/js/jquery.min.js\"></script>");
      out.write("\r\n");
      out.write("<!-- ./wrapper -->\r\n");
      out.write("<script type=\"text/javascript\">\r\n");
      out.write("    function verify()\r\n");
      out.write("    {\r\n");
      out.write("        console.log(`click`);\r\n");
      out.write("        //对数据进行检验\r\n");
      out.write("        let txtNum=$(`input[name=txtNum]`).val();\r\n");
      out.write("        if(txtNum==='')\r\n");
      out.write("        {\r\n");
      out.write("            alert(`学号不能为空`);\r\n");
      out.write("            $(`input[name=txtNum]`).focus();//光标选中\r\n");
      out.write("            return false;\r\n");
      out.write("        }\r\n");
      out.write("        let txtName=$(`input[name=txtName]`).val();\r\n");
      out.write("        if(txtName==='')\r\n");
      out.write("        {\r\n");
      out.write("            alert(`姓名不能为空`);\r\n");
      out.write("            $(`input[name=txtName]`).focus();//光标选中\r\n");
      out.write("            return false;\r\n");
      out.write("        }\r\n");
      out.write("        let txtClass=$(`input[name=txtClass]`).val();\r\n");
      out.write("        if(txtClass==='')\r\n");
      out.write("        {\r\n");
      out.write("            alert(`班级不能为空`);\r\n");
      out.write("            $(`input[name=txtClass]`).focus();//光标选中\r\n");
      out.write("            return false;\r\n");
      out.write("        }\r\n");
      out.write("        let txtSex=$(`input[name=txtSex]`).val();\r\n");
      out.write("        if(txtSex==='')\r\n");
      out.write("        {\r\n");
      out.write("            alert(`姓名不能为空`);\r\n");
      out.write("            $(`input[name=txtSex]`).focus();//光标选中\r\n");
      out.write("            return false;\r\n");
      out.write("        }\r\n");
      out.write("        let txtCourse=$(`input[name=txtCourse]`).val();\r\n");
      out.write("        if(txtCourse==='')\r\n");
      out.write("        {\r\n");
      out.write("            alert(`科目不能为空`);\r\n");
      out.write("            $(`input[name=txtCourse]`).focus();//光标选中\r\n");
      out.write("            return false;\r\n");
      out.write("        }\r\n");
      out.write("        let txtScore=$(`input[name=txtScore]`).val();\r\n");
      out.write("        if(txtScore==='')\r\n");
      out.write("        {\r\n");
      out.write("            alert(`成绩不能为空`);\r\n");
      out.write("            $(`input[name=txtScore]`).focus();//光标选中\r\n");
      out.write("            return false;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        let txtMajor=$(`input[name=txtMajor]`).val();\r\n");
      out.write("        if(txtMajor==='')\r\n");
      out.write("        {\r\n");
      out.write("            alert(`专业不能为空`);\r\n");
      out.write("            $(`input[name=txtMajor]`).focus();//光标选中\r\n");
      out.write("            return false;\r\n");
      out.write("        }\r\n");
      out.write("    }\r\n");
      out.write("</script>\r\n");
      out.write("</body>\r\n");
      out.write("</html>\r\n");
    } catch (java.lang.Throwable t) {
      if (!(t instanceof javax.servlet.jsp.SkipPageException)){
        out = _jspx_out;
        if (out != null && out.getBufferSize() != 0)
          try { out.clearBuffer(); } catch (java.io.IOException e) {}
        if (_jspx_page_context != null) _jspx_page_context.handlePageException(t);
        else throw new ServletException(t);
      }
    } finally {
      _jspxFactory.releasePageContext(_jspx_page_context);
    }
  }
}
