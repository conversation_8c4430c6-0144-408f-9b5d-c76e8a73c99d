package com.ntvu.studentmis.db;

import com.ntvu.studentmis.entity.Student;
import com.ntvu.studentmis.pager.PagerHelper;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;

/**
 * 对数据库操作的类
 */
public class DBStudent {
    //驱动类
    private final String driverClassName = "com.mysql.cj.jdbc.Driver";
    //连接数据库地址
    private final String url = "*****************************************";
    //连接数据库用户名
    private final String dbName = "root";
    //连接数据库密码
    private final String dbPwd = "xhxabc";

    //
    private Connection conn = null;
    private Statement stmt = null;
    private String sql = null;
    private ResultSet rs = null;

    /**
     * 初始化数据连接
     */
    private void init(){
        try {
            //加载驱动
            Class.forName(driverClassName);
            //获得与数据库的连接
            this.conn = DriverManager.getConnection(url,dbName,dbPwd);
            //获得招待句柄
            this.stmt = this.conn.createStatement();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 释放连接资源
     */
    private void release()
    {
        //关闭
        try {
            if(rs != null && !rs.isClosed())
            {
                rs.close();
                rs = null;
            }
            if(stmt != null && !stmt.isClosed()) {
                stmt.close();
                stmt = null;
            }
            if(conn != null && !conn.isClosed()) {
                conn.close();
                conn = null;
            }
        } catch (SQLException throwables) {
            throwables.printStackTrace();
        }
    }
    
    public boolean register(Student student)
    {
        boolean succeed = false;
        try{
            init();
            sql = "insert into Student(stu_num,stu_name,stu_sex,stu_age,stu_class,major,department)" +
                    " values(%s,%s,%s,%d,%s,%s,%s)";
            sql = String.format(sql,
                    "'" + student.getStu_num() + "'",
                    "'" + student.getStu_name() + "'",
                    "'" + student.getStu_sex() + "'",
                    student.getStu_age(),
                    "'" + student.getStu_class() + "'",
                    "'" + student.getMajor() + "'",
                    "'" + student.getDepartment() + "'");
            System.out.println(sql);
            //执行，返回受影响的记录行数
            int effectedRows = stmt.executeUpdate(sql);
            //使用结果
            //如果结果集不为空
            succeed = effectedRows > 0;
            //关闭
            release();
        }catch (Exception ex)
        {
            ex.printStackTrace();
        }
        return succeed;
    }
    
    public boolean delete(int id)
    {
        boolean succeed = false;
        try{
            init();
            sql = "delete from Student where stu_id = %d";
            sql = String.format(sql, id);
            System.out.println(sql);
            //执行，返回受影响的记录行数
            int effectedRows = stmt.executeUpdate(sql);
            //使用结果
            if(effectedRows > 0)
            {
                //如果结果集不为空
                succeed = true;
            }else{
                succeed = false;
            }
            //关闭
            release();
        }catch (Exception ex)
        {
            ex.printStackTrace();
        }
        return succeed;
    }

    public boolean edit(Student student)
    {
        boolean succeed = false;
        try{
            init();
            sql = "update Student set stu_num=%s,stu_name=%s,stu_sex=%s,stu_age=%d,stu_class=%s,major=%s,department=%s where stu_id = %d";
            sql = String.format(sql,
                    "'" + student.getStu_num() + "'",
                    "'" + student.getStu_name() + "'",
                    "'" + student.getStu_sex() + "'",
                    student.getStu_age(),
                    "'" + student.getStu_class() + "'",
                    "'" + student.getMajor() + "'",
                    "'" + student.getDepartment() + "'",
                    student.getStu_id());
            System.out.println(sql);
            //执行，返回受影响的记录行数
            int effectedRows = stmt.executeUpdate(sql);
            //使用结果
            //如果结果集不为空
            succeed = effectedRows > 0;
            //关闭
            release();
        }catch (Exception ex)
        {
            ex.printStackTrace();
        }
        return succeed;
    }

    //编辑删除
    public Student getListById(int id)
    {
        Student student = null;
        try {
            init();
            sql = "select * from student where stu_id = %d";
            sql = String.format(sql,id);
            //执行，获得返回结果
            System.out.println(sql);
            ResultSet rs = stmt.executeQuery(sql);
            //使用结果
            if(rs.next())
            {
                //如果结果集不为空
                student = new Student();
                student.setStu_id(rs.getInt("stu_id"));
                student.setStu_num(rs.getString("stu_num"));
                student.setStu_name(rs.getString("stu_name"));
                student.setStu_sex(rs.getString("stu_sex"));
                student.setStu_age(rs.getInt("stu_age"));
                student.setStu_class(rs.getString("stu_class"));
                student.setMajor(rs.getString("major"));
                student.setDepartment(rs.getString("department"));
            }
            //关闭
            release();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return student;
    }

    public int getRecordCount(String stu_num, String stu_name)
    {
        int count = 0;
        try {
            init();
            sql = "select count(*) from student where 1 = 1 ";
            if(stu_num != null && !stu_num.equals(""))
            {
                sql += " and stu_num like '%" + stu_num + "%'";
            }
            if(stu_name != null && !stu_name.equals(""))
            {
                sql += " and stu_name like '%" + stu_name + "%'";
            }
            //执行，获得返回结果
            System.out.println(sql);
            ResultSet rs = stmt.executeQuery(sql);
            //使用结果
            if(rs.next())
            {
                count = rs.getInt(1);
            }
            //关闭
            release();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return count;
    }
    
    public int getCount()throws SQLException, ClassNotFoundException
    {
        init();
        String sql = "SELECT COUNT(*) count FROM Student";
        ResultSet resultSet = stmt.executeQuery(sql);
        if (resultSet.next())
        {
            int count= resultSet.getInt("count");
            release();
            return count;
        }
        release();
        return 0;
    }

    public void getList(PagerHelper<Student> pager)
    {
        try {
            init();
            sql = "select * from Student where 1 = 1";
            if(pager.getQueryParams().containsKey("stu_num"))
            {
                sql += " and stu_num like '%" + pager.getQueryParams().get("stu_num") + "%'";
            }
            if(pager.getQueryParams().containsKey("stu_name"))
            {
                sql += " and stu_name like '%" + pager.getQueryParams().get("stu_name") + "%'";
            }
            //拼接：limit
            sql += " limit " + (pager.getPageIndex() - 1) * pager.getPageSize() + "," + pager.getPageSize();//limit 3,3
            //执行，获得返回结果
            System.out.println(sql);
            ResultSet rs = stmt.executeQuery(sql);
            //使用结果
            List<Student> lst = new ArrayList<>();
            while(rs.next())
            {
                //如果结果集不为空
                Student student = new Student();
                student.setStu_id(rs.getInt("stu_id"));
                student.setStu_num(rs.getString("stu_num"));
                student.setStu_name(rs.getString("stu_name"));
                student.setStu_sex(rs.getString("stu_sex"));
                student.setStu_age(rs.getInt("stu_age"));
                student.setStu_class(rs.getString("stu_class"));
                student.setMajor(rs.getString("major"));
                student.setDepartment(rs.getString("department"));
                lst.add(student);
            }
            pager.setData(lst);
            //关闭
            release();
            //
            int count = getRecordCount(pager.getQueryParams().get("stu_num"),pager.getQueryParams().get("stu_name"));
            pager.setRecordCount(count);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


}
