/*
 * Generated by the Jasper component of Apache Tomcat
 * Version: Apache Tomcat/7.0.47
 * Generated at: 2025-06-20 03:37:06 UTC
 * Note: The last modified time of this file was set to
 *       the last modified time of the source file after
 *       generation to assist with modification tracking.
 */
package org.apache.jsp;

import javax.servlet.*;
import javax.servlet.http.*;
import javax.servlet.jsp.*;

public final class login_jsp extends org.apache.jasper.runtime.HttpJspBase
    implements org.apache.jasper.runtime.JspSourceDependent {

  private static final javax.servlet.jsp.JspFactory _jspxFactory =
          javax.servlet.jsp.JspFactory.getDefaultFactory();

  private static java.util.Map<java.lang.String,java.lang.Long> _jspx_dependants;

  static {
    _jspx_dependants = new java.util.HashMap<java.lang.String,java.lang.Long>(1);
    _jspx_dependants.put("/include/foot_js.jsp", Long.valueOf(1685610773249L));
  }

  private javax.el.ExpressionFactory _el_expressionfactory;
  private org.apache.tomcat.InstanceManager _jsp_instancemanager;

  public java.util.Map<java.lang.String,java.lang.Long> getDependants() {
    return _jspx_dependants;
  }

  public void _jspInit() {
    _el_expressionfactory = _jspxFactory.getJspApplicationContext(getServletConfig().getServletContext()).getExpressionFactory();
    _jsp_instancemanager = org.apache.jasper.runtime.InstanceManagerFactory.getInstanceManager(getServletConfig());
  }

  public void _jspDestroy() {
  }

  public void _jspService(final javax.servlet.http.HttpServletRequest request, final javax.servlet.http.HttpServletResponse response)
        throws java.io.IOException, javax.servlet.ServletException {

    final javax.servlet.jsp.PageContext pageContext;
    javax.servlet.http.HttpSession session = null;
    final javax.servlet.ServletContext application;
    final javax.servlet.ServletConfig config;
    javax.servlet.jsp.JspWriter out = null;
    final java.lang.Object page = this;
    javax.servlet.jsp.JspWriter _jspx_out = null;
    javax.servlet.jsp.PageContext _jspx_page_context = null;


    try {
      response.setContentType("text/html; charset=UTF-8");
      pageContext = _jspxFactory.getPageContext(this, request, response,
      			null, true, 8192, true);
      _jspx_page_context = pageContext;
      application = pageContext.getServletContext();
      config = pageContext.getServletConfig();
      session = pageContext.getSession();
      out = pageContext.getOut();
      _jspx_out = out;

      out.write("\n");
      out.write("<!DOCTYPE html>\n");
      out.write("<html lang=\"zh-CN\">\n");
      out.write("<head>\n");
      out.write("  <meta charset=\"utf-8\">\n");
      out.write("  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\">\n");
      out.write("  <meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\">\n");
      out.write("  ");
      org.apache.jasper.runtime.JspRuntimeLibrary.include(request, response, "include/header_css.jsp", out, true);
      out.write("\n");
      out.write("  <title>学生成绩管理系统</title>\n");
      out.write("  <style>\n");
      out.write("    /* 中文字体优化 */\n");
      out.write("    body, .login-logo, .form-control, .btn, .alert {\n");
      out.write("      font-family: \"Microsoft YaHei\", \"微软雅黑\", \"Helvetica Neue\", Helvetica, Arial, sans-serif;\n");
      out.write("    }\n");
      out.write("\n");
      out.write("    .login-logo {\n");
      out.write("      font-size: 2.1rem;\n");
      out.write("      font-weight: 600;\n");
      out.write("      color: #495057;\n");
      out.write("      text-align: center;\n");
      out.write("      margin-bottom: 20px;\n");
      out.write("    }\n");
      out.write("\n");
      out.write("    .form-control {\n");
      out.write("      font-size: 14px;\n");
      out.write("      padding: 10px 12px;\n");
      out.write("    }\n");
      out.write("\n");
      out.write("    .btn-primary {\n");
      out.write("      font-size: 16px;\n");
      out.write("      font-weight: 500;\n");
      out.write("      padding: 10px 20px;\n");
      out.write("    }\n");
      out.write("\n");
      out.write("    .alert-info {\n");
      out.write("      background-color: #e3f2fd;\n");
      out.write("      border-color: #bbdefb;\n");
      out.write("      color: #1976d2;\n");
      out.write("    }\n");
      out.write("\n");
      out.write("    /* 错误消息样式 */\n");
      out.write("    .error-msg {\n");
      out.write("      color: #dc3545;\n");
      out.write("      font-size: 14px;\n");
      out.write("      text-align: center;\n");
      out.write("      margin-top: 10px;\n");
      out.write("    }\n");
      out.write("\n");
      out.write("    /* 修复错误消息显示问题 */\n");
      out.write("    .alert-danger {\n");
      out.write("      background-color: #f8d7da !important;\n");
      out.write("      border-color: #f5c6cb !important;\n");
      out.write("      color: #721c24 !important;\n");
      out.write("      padding: 12px !important;\n");
      out.write("      border-radius: 6px !important;\n");
      out.write("      margin-bottom: 15px !important;\n");
      out.write("      font-weight: 500 !important;\n");
      out.write("    }\n");
      out.write("\n");
      out.write("    .alert-danger i {\n");
      out.write("      margin-right: 8px;\n");
      out.write("    }\n");
      out.write("  </style>\n");
      out.write("</head>\n");
      out.write("<body class=\"hold-transition login-page\">\n");
      out.write("<div class=\"login-box\">\n");
      out.write("  <div class=\"login-logo\">\n");
      out.write("    <i class=\"fas fa-graduation-cap\"></i> 学生成绩管理系统\n");
      out.write("  </div>\n");
      out.write("  <!-- /.login-logo -->\n");
      out.write("  <div class=\"card\">\n");
      out.write("    <div class=\"card-body login-card-body\">\n");
      out.write("      <form action=\"Login\" method=\"post\" onsubmit=\"return verify()\" id=\"form1\">\n");
      out.write("        <div class=\"input-group mb-3\">\n");
      out.write("          <input name=\"txtUserName\" type=\"text\" class=\"form-control\" placeholder=\"请输入学工号\" value=\"");
      out.print( session.getAttribute("user_num")==null?"":session.getAttribute("user_num"));
      out.write("\" >\n");
      out.write("          <div class=\"input-group-append\">\n");
      out.write("            <div class=\"input-group-text\">\n");
      out.write("              <span class=\"fas fa-user\"></span>\n");
      out.write("            </div>\n");
      out.write("          </div>\n");
      out.write("        </div>\n");
      out.write("        <div class=\"input-group mb-3\">\n");
      out.write("          <input name=\"txtPassword\" type=\"password\" class=\"form-control\" placeholder=\"请输入密码\" value=\"");
      out.print( session.getAttribute("password")==null?"":session.getAttribute("password"));
      out.write("\">\n");
      out.write("          <div class=\"input-group-append\">\n");
      out.write("            <div class=\"input-group-text\">\n");
      out.write("              <span class=\"fas fa-lock\"></span>\n");
      out.write("            </div>\n");
      out.write("          </div>\n");
      out.write("        </div>\n");
      out.write("        <div class=\"row\">\n");
      out.write("          <!-- /.col -->\n");
      out.write("          <div class=\"col-3\"></div>\n");
      out.write("          <div class=\"col-6\">\n");
      out.write("            <input type=\"submit\" class=\"btn btn-primary btn-block\" value=\"登录\">\n");
      out.write("          </div>\n");
      out.write("          <div class=\"col-3\"></div>\n");
      out.write("          <!-- /.col -->\n");
      out.write("        </div>\n");
      out.write("        <div class=\"row\">\n");
      out.write("          <div class=\"col-12\">\n");
      out.write("            ");

              Object obj = request.getSession().getAttribute("errorMsg");
              if (obj != null && !obj.toString().trim().isEmpty()) {
            
      out.write("\n");
      out.write("            <div class=\"alert alert-danger error-msg\" role=\"alert\">\n");
      out.write("              <i class=\"fas fa-exclamation-triangle\"></i> ");
      out.print(obj);
      out.write("\n");
      out.write("            </div>\n");
      out.write("            ");

                // 清除错误消息，避免重复显示
                request.getSession().removeAttribute("errorMsg");
              }
            
      out.write("\n");
      out.write("          </div>\n");
      out.write("        </div>\n");
      out.write("      </form>\n");
      out.write("      <div class=\"row\">\n");
      out.write("          <div class=\"col-9\">\n");
      out.write("            <a href=\"forgot_password.jsp\" class=\"text-center\">忘记密码?</a>\n");
      out.write("          </div>\n");
      out.write("          <div class=\"col-3\">\n");
      out.write("          <a href=\"register.jsp\" class=\"text-center\">注册账号</a>\n");
      out.write("          </div>\n");
      out.write("      </div>\n");
      out.write("\n");
      out.write("\n");
      out.write("    </div>\n");
      out.write("    <!-- /.login-card-body -->\n");
      out.write("  </div>\n");
      out.write("</div>\n");
      out.write("<!-- /.login-box -->\n");
      out.write("</body>\n");
      out.write("</html>\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("<!-- jQuery -->\r\n");
      out.write("<script src=\"");
      out.print( request.getContextPath());
      out.write("/plugins/jquery/jquery.min.js\"></script>\r\n");
      out.write("<!-- jQuery UI 1.11.4 -->\r\n");
      out.write("<script src=\"");
      out.print( request.getContextPath());
      out.write("/plugins/jquery-ui/jquery-ui.min.js\"></script>\r\n");
      out.write("<!-- Resolve conflict in jQuery UI tooltip with Bootstrap tooltip -->\r\n");
      out.write("<script>\r\n");
      out.write("    $.widget.bridge('uibutton', $.ui.button)\r\n");
      out.write("</script>\r\n");
      out.write("\r\n");
      out.write("<!-- Bootstrap 4 -->\r\n");
      out.write("<script src=\"");
      out.print( request.getContextPath());
      out.write("/plugins/bootstrap/js/bootstrap.bundle.min.js\"></script>\r\n");
      out.write("<!-- ChartJS -->\r\n");
      out.write("<script src=\"");
      out.print( request.getContextPath());
      out.write("/plugins/chart.js/Chart.min.js\"></script>\r\n");
      out.write("<!-- Sparkline -->\r\n");
      out.write("<script src=\"");
      out.print( request.getContextPath());
      out.write("/plugins/sparklines/sparkline.js\"></script>\r\n");
      out.write("<!-- JQVMap -->\r\n");
      out.write("<script src=\"");
      out.print( request.getContextPath());
      out.write("/plugins/jqvmap/jquery.vmap.min.js\"></script>\r\n");
      out.write("<script src=\"");
      out.print( request.getContextPath());
      out.write("/plugins/jqvmap/maps/jquery.vmap.usa.js\"></script>\r\n");
      out.write("<!-- jQuery Knob Chart -->\r\n");
      out.write("<script src=\"");
      out.print( request.getContextPath());
      out.write("/plugins/jquery-knob/jquery.knob.min.js\"></script>\r\n");
      out.write("<!-- daterangepicker -->\r\n");
      out.write("<script src=\"");
      out.print( request.getContextPath());
      out.write("/plugins/moment/moment.min.js\"></script>\r\n");
      out.write("<script src=\"");
      out.print( request.getContextPath());
      out.write("/plugins/daterangepicker/daterangepicker.js\"></script>\r\n");
      out.write("<!-- Tempusdominus Bootstrap 4 -->\r\n");
      out.write("<script src=\"");
      out.print( request.getContextPath());
      out.write("/plugins/tempusdominus-bootstrap-4/js/tempusdominus-bootstrap-4.min.js\"></script>\r\n");
      out.write("<!-- Summernote -->\r\n");
      out.write("<script src=\"");
      out.print( request.getContextPath());
      out.write("/plugins/summernote/summernote-bs4.min.js\"></script>\r\n");
      out.write("<!-- overlayScrollbars -->\r\n");
      out.write("<script src=\"");
      out.print( request.getContextPath());
      out.write("/plugins/overlayScrollbars/js/jquery.overlayScrollbars.min.js\"></script>\r\n");
      out.write("<!-- AdminLTE App -->\r\n");
      out.write("<script src=\"");
      out.print( request.getContextPath());
      out.write("/dist/js/adminlte.js\"></script>\r\n");
      out.write("<!-- AdminLTE for demo purposes -->\r\n");
      out.write("<script src=\"");
      out.print( request.getContextPath());
      out.write("/dist/js/demo.js\"></script>\r\n");
      out.write("<!-- AdminLTE dashboard demo (This is only for demo purposes) -->\r\n");
      out.write("<script src=\"");
      out.print( request.getContextPath());
      out.write("/dist/js/pages/dashboard.js\"></script>\r\n");
      out.write("<script type=\"text/javascript\" src=\"");
      out.print( request.getContextPath());
      out.write("/js/jquery-3.6.1.min.js\"></script>\r\n");
      out.write("<script type=\"text/javascript\" src=\"");
      out.print( request.getContextPath());
      out.write("/js/jquery.min.js\"></script>");
      out.write("\n");
      out.write("<script type=\"text/javascript\">\n");
      out.write("  //绑定按钮事件\n");
      out.write("  function verify() {\n");
      out.write("    console.log(`click`);\n");
      out.write("    //对数据进行检验\n");
      out.write("    let txtUserName=$(`input[name=txtUserName]`).val();\n");
      out.write("    if(txtUserName==='')\n");
      out.write("    {\n");
      out.write("      alert(`登录名称不能为空`);\n");
      out.write("      $(`input[name=txtUserName]`).focus();//光标选中\n");
      out.write("      return false;\n");
      out.write("    }\n");
      out.write("    let txtPassword=$(`input[name=txtPassword]`).val();\n");
      out.write("    if(txtPassword==='')\n");
      out.write("    {\n");
      out.write("      alert(`密码不能为空`);\n");
      out.write("      $(`input[name=txtPassword]`).focus();//光标选中\n");
      out.write("      return false;\n");
      out.write("    }\n");
      out.write("  }\n");
      out.write("</script>\n");
    } catch (java.lang.Throwable t) {
      if (!(t instanceof javax.servlet.jsp.SkipPageException)){
        out = _jspx_out;
        if (out != null && out.getBufferSize() != 0)
          try { out.clearBuffer(); } catch (java.io.IOException e) {}
        if (_jspx_page_context != null) _jspx_page_context.handlePageException(t);
        else throw new ServletException(t);
      }
    } finally {
      _jspxFactory.releasePageContext(_jspx_page_context);
    }
  }
}
