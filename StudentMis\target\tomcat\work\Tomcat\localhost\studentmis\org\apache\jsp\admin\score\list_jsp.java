/*
 * Generated by the Jasper component of Apache Tomcat
 * Version: Apache Tomcat/7.0.47
 * Generated at: 2025-06-20 03:50:33 UTC
 * Note: The last modified time of this file was set to
 *       the last modified time of the source file after
 *       generation to assist with modification tracking.
 */
package org.apache.jsp.admin.score;

import javax.servlet.*;
import javax.servlet.http.*;
import javax.servlet.jsp.*;
import com.ntvu.studentmis.pager.PagerHelper;
import com.ntvu.studentmis.entity.Score;
import com.ntvu.studentmis.util.WebTools;
import com.ntvu.studentmis.db.DBScore;
import jxl.write.WritableWorkbook;
import jxl.Workbook;
import java.io.File;
import jxl.write.WritableSheet;
import jxl.write.Label;
import jxl.write.WriteException;
import com.ntvu.studentmis.entity.User;
import com.ntvu.studentmis.db.DBManager;

public final class list_jsp extends org.apache.jasper.runtime.HttpJspBase
    implements org.apache.jasper.runtime.JspSourceDependent {

  private static final javax.servlet.jsp.JspFactory _jspxFactory =
          javax.servlet.jsp.JspFactory.getDefaultFactory();

  private static java.util.Map<java.lang.String,java.lang.Long> _jspx_dependants;

  static {
    _jspx_dependants = new java.util.HashMap<java.lang.String,java.lang.Long>(5);
    _jspx_dependants.put("/admin/score/../../include/pager_footer.jsp", Long.valueOf(1685532590406L));
    _jspx_dependants.put("/admin/score/../../include/left_menu.jsp", Long.valueOf(1750296532725L));
    _jspx_dependants.put("/admin/score/../../include/header_nav.jsp", Long.valueOf(1750250240204L));
    _jspx_dependants.put("/admin/score/../../include/../include/menu.jsp", Long.valueOf(1672200865841L));
    _jspx_dependants.put("/admin/score/../../include/foot_js.jsp", Long.valueOf(1685610773249L));
  }

  private javax.el.ExpressionFactory _el_expressionfactory;
  private org.apache.tomcat.InstanceManager _jsp_instancemanager;

  public java.util.Map<java.lang.String,java.lang.Long> getDependants() {
    return _jspx_dependants;
  }

  public void _jspInit() {
    _el_expressionfactory = _jspxFactory.getJspApplicationContext(getServletConfig().getServletContext()).getExpressionFactory();
    _jsp_instancemanager = org.apache.jasper.runtime.InstanceManagerFactory.getInstanceManager(getServletConfig());
  }

  public void _jspDestroy() {
  }

  public void _jspService(final javax.servlet.http.HttpServletRequest request, final javax.servlet.http.HttpServletResponse response)
        throws java.io.IOException, javax.servlet.ServletException {

    final javax.servlet.jsp.PageContext pageContext;
    javax.servlet.http.HttpSession session = null;
    final javax.servlet.ServletContext application;
    final javax.servlet.ServletConfig config;
    javax.servlet.jsp.JspWriter out = null;
    final java.lang.Object page = this;
    javax.servlet.jsp.JspWriter _jspx_out = null;
    javax.servlet.jsp.PageContext _jspx_page_context = null;


    try {
      response.setContentType("text/html; charset=UTF-8");
      pageContext = _jspxFactory.getPageContext(this, request, response,
      			null, true, 8192, true);
      _jspx_page_context = pageContext;
      application = pageContext.getServletContext();
      config = pageContext.getServletConfig();
      session = pageContext.getSession();
      out = pageContext.getOut();
      _jspx_out = out;

      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("<!DOCTYPE html>\r\n");
      out.write("<html lang=\"en\">\r\n");
      out.write("<head>\r\n");
      out.write("    <meta charset=\"utf-8\">\r\n");
      out.write("    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\">\r\n");
      out.write("    ");
      org.apache.jasper.runtime.JspRuntimeLibrary.include(request, response, "../../include/header_css.jsp", out, true);
      out.write("\r\n");
      out.write("    <title>学生成绩列表</title>\r\n");
      out.write("</head>\r\n");
      out.write("<body class=\"hold-transition sidebar-mini layout-fixed\">\r\n");
      out.write("<div class=\"wrapper\">\r\n");
      out.write("    ");
      out.write('\r');
      out.write('\n');
      out.write("\r\n");
      out.write("\r\n");
      out.write('\r');
      out.write('\n');

    //用户没登录则不允许访问登录页面
    if(session.getAttribute("curUserName")==null)
    {
        response.sendRedirect("../../login.jsp");
        return;
    }
    //取得当前环境名称 /javaweb
    String contextPath=request.getContextPath();

      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("<!-- Preloader -->\r\n");
      out.write('\r');
      out.write('\n');
      out.write('\r');
      out.write('\n');
      out.write('\r');
      out.write('\n');

    String curUserName=(String)session.getAttribute("curUserName");

      out.write("\r\n");
      out.write("<!-- Navbar -->\r\n");
      out.write("<nav class=\"main-header navbar navbar-expand navbar-white navbar-light\">\r\n");
      out.write("    <!-- Left navbar links -->\r\n");
      out.write("    <ul class=\"navbar-nav\">\r\n");
      out.write("        <li class=\"nav-item\">\r\n");
      out.write("            <a class=\"nav-link\" data-widget=\"pushmenu\" href=\"#\" role=\"button\"><i class=\"fas fa-bars\"></i></a>\r\n");
      out.write("        </li>\r\n");
      out.write("        <li class=\"nav-item d-none d-sm-inline-block\">\r\n");
      out.write("            <a href=\"");
      out.print( request.getContextPath() + "/admin/user/list.jsp");
      out.write("\" class=\"nav-link\" style=\"color: blue\">主页</a>\r\n");
      out.write("        </li>\r\n");
      out.write("\r\n");
      out.write("        <li class=\"nav-item d-none d-sm-inline-block\">\r\n");
      out.write("            <a href=\"");
      out.print( contextPath );
      out.write("/LoginOut\" class=\"nav-link\" style=\"color: #ff0000\">退出</a>\r\n");
      out.write("        </li>\r\n");
      out.write("    </ul>\r\n");
      out.write("\r\n");
      out.write("    <!-- Right navbar links -->\r\n");
      out.write("    <ul class=\"navbar-nav ml-auto\">\r\n");
      out.write("        <!-- Navbar Search -->\r\n");
      out.write("        <li class=\"nav-item\">\r\n");
      out.write("            <a class=\"nav-link\" data-widget=\"navbar-search\" href=\"#\" role=\"button\">\r\n");
      out.write("                <i class=\"fas fa-search\"></i>\r\n");
      out.write("            </a>\r\n");
      out.write("            <div class=\"navbar-search-block\">\r\n");
      out.write("                <form class=\"form-inline\">\r\n");
      out.write("                    <div class=\"input-group input-group-sm\">\r\n");
      out.write("                        <input class=\"form-control form-control-navbar\" type=\"search\" placeholder=\"Search\" aria-label=\"Search\">\r\n");
      out.write("                        <div class=\"input-group-append\">\r\n");
      out.write("                            <button class=\"btn btn-navbar\" type=\"submit\">\r\n");
      out.write("                                <i class=\"fas fa-search\"></i>\r\n");
      out.write("                            </button>\r\n");
      out.write("                            <button class=\"btn btn-navbar\" type=\"button\" data-widget=\"navbar-search\">\r\n");
      out.write("                                <i class=\"fas fa-times\"></i>\r\n");
      out.write("                            </button>\r\n");
      out.write("                        </div>\r\n");
      out.write("                    </div>\r\n");
      out.write("                </form>\r\n");
      out.write("            </div>\r\n");
      out.write("        </li>\r\n");
      out.write("\r\n");
      out.write("        <li class=\"nav-item\">\r\n");
      out.write("            <a class=\"nav-link\" data-widget=\"fullscreen\" href=\"#\" role=\"button\">\r\n");
      out.write("                <i class=\"fas fa-expand-arrows-alt\"></i>\r\n");
      out.write("            </a>\r\n");
      out.write("        </li>\r\n");
      out.write("        <li class=\"nav-item\">\r\n");
      out.write("            <a class=\"nav-link\" data-widget=\"control-sidebar\" data-controlsidebar-slide=\"true\" href=\"#\" role=\"button\">\r\n");
      out.write("                <i class=\"fas fa-th-large\"></i>\r\n");
      out.write("            </a>\r\n");
      out.write("        </li>\r\n");
      out.write("    </ul>\r\n");
      out.write("</nav>\r\n");
      out.write("<!-- /.navbar -->");
      out.write("\r\n");
      out.write("    ");
      out.write('\r');
      out.write('\n');
      out.write("\r\n");
      out.write("\r\n");
      out.write("<!-- Main Sidebar Container -->\r\n");
      out.write("<aside class=\"main-sidebar sidebar-dark-primary elevation-4\">\r\n");
      out.write("    <!-- Brand Logo -->\r\n");
      out.write("    <!-- Sidebar -->\r\n");
      out.write("    <div class=\"sidebar\">\r\n");
      out.write("        <!-- Sidebar user panel (optional) -->\r\n");
      out.write("        <div class=\"user-panel mt-3 pb-3 mb-3 d-flex\">\r\n");
      out.write("            ");

                String login_name=(String)session.getAttribute("curUserName");
                User g_user=new DBManager().getDetails(login_name);
            
      out.write("\r\n");
      out.write("            <div class=\"image\">\r\n");
      out.write("                ");
      out.write("\r\n");
      out.write("                <img src=\"");
      out.print( request.getContextPath() + "/uploadPic/user3.jpg");
      out.write("\" class=\"img-circle elevation-2\" alt=\"User Image\">\r\n");
      out.write("            </div>\r\n");
      out.write("            <div class=\"info\">\r\n");
      out.write("                <a href=\"");
      out.print( request.getContextPath() + "/include/info.jsp");
      out.write("\" class=\"d-block\" style=\"color: white\">");
      out.print( session.getAttribute("curUserName"));
      out.write("</a>\r\n");
      out.write("            </div>\r\n");
      out.write("        </div>\r\n");
      out.write("\r\n");
      out.write("        <!-- Sidebar Menu -->\r\n");
      out.write("        <nav class=\"mt-2\">\r\n");
      out.write("            <ul class=\"nav nav-pills nav-sidebar flex-column\" data-widget=\"treeview\" role=\"menu\" data-accordion=\"false\">\r\n");
      out.write("                <!-- Add icons to the links using the .nav-icon class\r\n");
      out.write("                     with font-awesome or any other icon font library -->\r\n");
      out.write("                <li class=\"nav-item\">\r\n");
      out.write("                    <a href=\"#\" class=\"nav-link active\">\r\n");
      out.write("                        <i class=\"nav-icon fas fa-user\"></i>\r\n");
      out.write("                        <p>\r\n");
      out.write("                            用户管理\r\n");
      out.write("                            <i class=\"right fas fa-angle-left\"></i>\r\n");
      out.write("                        </p>\r\n");
      out.write("                    </a>\r\n");
      out.write("                    <ul class=\"nav nav-treeview\">\r\n");
      out.write("                        <li class=\"nav-item\">\r\n");
      out.write("                            <a href=\"");
      out.print( request.getContextPath() + "/admin/user/list.jsp");
      out.write("\" class=\"nav-link\">\r\n");
      out.write("                                <i class=\"far fa-circle nav-icon\"></i>\r\n");
      out.write("                                <p>用户列表</p>\r\n");
      out.write("                            </a>\r\n");
      out.write("                        </li>\r\n");
      out.write("                        <li class=\"nav-item\">\r\n");
      out.write("                            <a href=\"");
      out.print( request.getContextPath() + "/admin/user/add.jsp");
      out.write("\" class=\"nav-link\">\r\n");
      out.write("                                <i class=\"far fa-circle nav-icon\"></i>\r\n");
      out.write("                                <p>添加用户</p>\r\n");
      out.write("                            </a>\r\n");
      out.write("                        </li>\r\n");
      out.write("                    </ul>\r\n");
      out.write("                </li>\r\n");
      out.write("                <li class=\"nav-item\">\r\n");
      out.write("                    <a href=\"#\" class=\"nav-link active\">\r\n");
      out.write("                        <i class=\"nav-icon fas fa-user\"></i>\r\n");
      out.write("                        <p>\r\n");
      out.write("                            学生信息管理\r\n");
      out.write("                            <i class=\"right fas fa-angle-left\"></i>\r\n");
      out.write("                        </p>\r\n");
      out.write("                    </a>\r\n");
      out.write("                    <ul class=\"nav nav-treeview\">\r\n");
      out.write("                        <li class=\"nav-item\">\r\n");
      out.write("                            <a href=\"");
      out.print( request.getContextPath() + "/admin/student/list.jsp");
      out.write("\" class=\"nav-link\">\r\n");
      out.write("                                <i class=\"far fa-circle nav-icon\"></i>\r\n");
      out.write("                                <p>学生信息列表</p>\r\n");
      out.write("                            </a>\r\n");
      out.write("                        </li>\r\n");
      out.write("                        <li class=\"nav-item\">\r\n");
      out.write("                            <a href=\"");
      out.print( request.getContextPath() + "/admin/student/add.jsp");
      out.write("\" class=\"nav-link\">\r\n");
      out.write("                                <i class=\"far fa-circle nav-icon\"></i>\r\n");
      out.write("                                <p>添加学生信息</p>\r\n");
      out.write("                            </a>\r\n");
      out.write("                        </li>\r\n");
      out.write("                    </ul>\r\n");
      out.write("                </li>\r\n");
      out.write("                <li class=\"nav-item\">\r\n");
      out.write("                    <a href=\"#\" class=\"nav-link active\">\r\n");
      out.write("                        <i class=\"nav-icon fas fa-user\"></i>\r\n");
      out.write("                        <p>\r\n");
      out.write("                            课程信息管理\r\n");
      out.write("                            <i class=\"right fas fa-angle-left\"></i>\r\n");
      out.write("                        </p>\r\n");
      out.write("                    </a>\r\n");
      out.write("                    <ul class=\"nav nav-treeview\">\r\n");
      out.write("                        <li class=\"nav-item\">\r\n");
      out.write("                            <a href=\"");
      out.print( request.getContextPath() + "/admin/course/list.jsp");
      out.write("\" class=\"nav-link\">\r\n");
      out.write("                                <i class=\"far fa-circle nav-icon\"></i>\r\n");
      out.write("                                <p>课程信息列表</p>\r\n");
      out.write("                            </a>\r\n");
      out.write("                        </li>\r\n");
      out.write("                        <li class=\"nav-item\">\r\n");
      out.write("                            <a href=\"");
      out.print( request.getContextPath() + "/admin/course/add.jsp");
      out.write("\" class=\"nav-link\">\r\n");
      out.write("                                <i class=\"far fa-circle nav-icon\"></i>\r\n");
      out.write("                                <p>添加课程信息</p>\r\n");
      out.write("                            </a>\r\n");
      out.write("                        </li>\r\n");
      out.write("                    </ul>\r\n");
      out.write("                </li>\r\n");
      out.write("                <li class=\"nav-item\">\r\n");
      out.write("                    <a href=\"#\" class=\"nav-link active\">\r\n");
      out.write("                        <i class=\"nav-icon fas fa-user\"></i>\r\n");
      out.write("                        <p>\r\n");
      out.write("                            学生成绩管理\r\n");
      out.write("                            <i class=\"right fas fa-angle-left\"></i>\r\n");
      out.write("                        </p>\r\n");
      out.write("                    </a>\r\n");
      out.write("                    <ul class=\"nav nav-treeview\">\r\n");
      out.write("                        <li class=\"nav-item\">\r\n");
      out.write("                            <a href=\"");
      out.print( request.getContextPath() + "/admin/score/list.jsp");
      out.write("\" class=\"nav-link\">\r\n");
      out.write("                                <i class=\"far fa-circle nav-icon\"></i>\r\n");
      out.write("                                <p>学生成绩列表</p>\r\n");
      out.write("                            </a>\r\n");
      out.write("                        </li>\r\n");
      out.write("                        <li class=\"nav-item\">\r\n");
      out.write("                            <a href=\"");
      out.print( request.getContextPath() + "/admin/score/add.jsp");
      out.write("\" class=\"nav-link\">\r\n");
      out.write("                                <i class=\"far fa-circle nav-icon\"></i>\r\n");
      out.write("                                <p>添加学生成绩</p>\r\n");
      out.write("                            </a>\r\n");
      out.write("                        </li>\r\n");
      out.write("                        <li class=\"nav-item\">\r\n");
      out.write("                            <a href=\"");
      out.print( request.getContextPath() + "/admin/student/ToExcel");
      out.write("\" class=\"nav-link\">\r\n");
      out.write("                                <i class=\"far fa-circle nav-icon\"></i>\r\n");
      out.write("                                <p>学生成绩报表</p>\r\n");
      out.write("                            </a>\r\n");
      out.write("                        </li>\r\n");
      out.write("                    </ul>\r\n");
      out.write("                </li>\r\n");
      out.write("                <li class=\"nav-item\">\r\n");
      out.write("                    <a href=\"#\" class=\"nav-link active\">\r\n");
      out.write("                        <i class=\"nav-icon fas fa-user\"></i>\r\n");
      out.write("                        <p>\r\n");
      out.write("                           教师信息管理\r\n");
      out.write("                            <i class=\"right fas fa-angle-left\"></i>\r\n");
      out.write("                        </p>\r\n");
      out.write("                    </a>\r\n");
      out.write("                    <ul class=\"nav nav-treeview\">\r\n");
      out.write("                        <li class=\"nav-item\">\r\n");
      out.write("                            <a href=\"");
      out.print( request.getContextPath() + "/admin/teacher/list.jsp");
      out.write("\" class=\"nav-link\">\r\n");
      out.write("                                <i class=\"far fa-circle nav-icon\"></i>\r\n");
      out.write("                                <p> 教师信息列表</p>\r\n");
      out.write("                            </a>\r\n");
      out.write("                        </li>\r\n");
      out.write("                        <li class=\"nav-item\">\r\n");
      out.write("                            <a href=\"");
      out.print( request.getContextPath() + "/admin/teacher/add.jsp");
      out.write("\" class=\"nav-link\">\r\n");
      out.write("                                <i class=\"far fa-circle nav-icon\"></i>\r\n");
      out.write("                                <p>添加教师信息</p>\r\n");
      out.write("                            </a>\r\n");
      out.write("                        </li>\r\n");
      out.write("                    </ul>\r\n");
      out.write("                </li>\r\n");
      out.write("            </ul>\r\n");
      out.write("        </nav>\r\n");
      out.write("        <!-- /.sidebar-menu -->\r\n");
      out.write("    </div>\r\n");
      out.write("    <!-- /.sidebar -->\r\n");
      out.write("</aside>\r\n");
      out.write("\r\n");
      out.write("    ");

        request.setCharacterEncoding("utf-8");
        String stu_num = request.getParameter("stu_num");
        String stu_name = request.getParameter("stu_name");
        PagerHelper<Score> pager = new PagerHelper(request);
        if (stu_num != null && !stu_num.trim().equals("")) {
            pager.getQueryParams().put("stu_num", stu_num);
        }
        if (stu_name != null && !stu_name.trim().equals("")) {
            pager.getQueryParams().put("stu_name", stu_name);
        }
        new DBScore().getList(pager);
        double sumScore=pager.getSumScore();
        double avgScore=pager.getAvgScore();
    
      out.write("\r\n");
      out.write("    <!-- Content Wrapper. Contains page content -->\r\n");
      out.write("    <form id=\"form1\" name=\"form1\" method=\"post\"\r\n");
      out.write("          action=\"");
      out.print( request.getContextPath() + "/admin/score/ScoreServlet?action=add");
      out.write("\">\r\n");
      out.write("        <div class=\"content-wrapper\">\r\n");
      out.write("            <section class=\"content\">\r\n");
      out.write("                <div class=\"container-fluid\">\r\n");
      out.write("                    <div class=\"row\">\r\n");
      out.write("                        <div class=\"col-12\">\r\n");
      out.write("                            <div class=\"card\">\r\n");
      out.write("                                <div class=\"card-header\">\r\n");
      out.write("                                    <div class=\"row\">\r\n");
      out.write("\r\n");
      out.write("                                        <div class=\"col-lg-2\">学号：<input type=\"text\" name=\"stu_num\"\r\n");
      out.write("                                                                          class=\"form-control\" value=\"");
      out.print( WebTools.parseNullorEmpty(stu_num));
      out.write("\"></div>\r\n");
      out.write("                                        <div class=\"col-lg-2\">姓名：<input type=\"text\" name=\"stu_name\"\r\n");
      out.write("                                                                          class=\"form-control\" value=\"");
      out.print( WebTools.parseNullorEmpty(stu_name));
      out.write("\"></div>\r\n");
      out.write("                                        <div class=\"col-lg-2\">\r\n");
      out.write("                                            <input type=\"button\" class=\"btn btn-block btn-primary btn-xs;form-control\"\r\n");
      out.write("                                                    style=\"margin-top: 25px;\" name=\"btnFind\" value=\"查询\">\r\n");
      out.write("                                        </div>\r\n");
      out.write("                                        <div class=\"col-lg-2\">\r\n");
      out.write("                                            <input type=\"button\" class=\"btn btn-block btn-danger btn-xs;form-control\"\r\n");
      out.write("                                                    style=\"margin-top: 25px;\" name=\"btnDelSel\" value=\"删除所勾选的\">\r\n");
      out.write("                                        </div>\r\n");
      out.write("                                        <div class=\"col-lg-2\">此学生总成绩：<input type=\"text\" class=\"form-control\" disabled=\"disabled\" value=\"");
      out.print( sumScore==0.0?"":sumScore);
      out.write("\" placeholder=\"点击查询后显示\"></div>\r\n");
      out.write("                                        <div class=\"col-lg-2\">此学生平均分：<input type=\"text\" class=\"form-control\" disabled=\"disabled\" value=\"");
      out.print( avgScore==0.0?"":avgScore);
      out.write("\" placeholder=\"点击查询后显示\"></div>\r\n");
      out.write("                                        <div class=\"col-lg-2\">平均绩点：<input type=\"text\" class=\"form-control\" disabled=\"disabled\" value=\"");
      out.print( avgScore==0.0?"":String.format("%.2f", com.ntvu.studentmis.entity.Score.calculateGPA(avgScore)));
      out.write("\" placeholder=\"点击查询后显示\" style=\"color: #007bff; font-weight: bold;\"></div>\r\n");
      out.write("                                    </div>\r\n");
      out.write("                                    <!-- /.card-header -->\r\n");
      out.write("                                    <div class=\"card-body\">\r\n");
      out.write("                                        <div class=\"row\">\r\n");
      out.write("                                            <div class=\"col-sm-12\">\r\n");
      out.write("                                                <table id=\"example1\"\r\n");
      out.write("                                                       class=\"table table-bordered table-striped dataTable dtr-inline\"\r\n");
      out.write("                                                       aria-describedby=\"example1_info\">\r\n");
      out.write("                                                    <thead>\r\n");
      out.write("                                                    <tr>\r\n");
      out.write("                                                        <th class=\"sorting sorting_asc\" tabindex=\"0\"\r\n");
      out.write("                                                            aria-controls=\"example1\" rowspan=\"1\" colspan=\"1\"\r\n");
      out.write("                                                            aria-sort=\"ascending\"\r\n");
      out.write("                                                            aria-label=\"Rendering engine: activate to sort column descending\">\r\n");
      out.write("                                                            <input type=\"checkbox\" class=\"form-check\" title=\"全选\" name=\"checkAll\">\r\n");
      out.write("                                                        </th>\r\n");
      out.write("                                                        <th class=\"sorting\" tabindex=\"0\" aria-controls=\"example1\"\r\n");
      out.write("                                                            rowspan=\"1\" colspan=\"1\"\r\n");
      out.write("                                                            aria-label=\"Platform(s): activate to sort column ascending\">\r\n");
      out.write("                                                            序号\r\n");
      out.write("                                                        </th>\r\n");
      out.write("                                                        <th class=\"sorting\" tabindex=\"0\" aria-controls=\"example1\"\r\n");
      out.write("                                                            rowspan=\"1\" colspan=\"1\"\r\n");
      out.write("                                                            aria-label=\"Browser: activate to sort column ascending\">\r\n");
      out.write("                                                            学号\r\n");
      out.write("                                                        </th>\r\n");
      out.write("                                                        <th class=\"sorting\" tabindex=\"0\" aria-controls=\"example1\"\r\n");
      out.write("                                                            rowspan=\"1\" colspan=\"1\"\r\n");
      out.write("                                                            aria-label=\"Platform(s): activate to sort column ascending\">\r\n");
      out.write("                                                            姓名\r\n");
      out.write("                                                        </th>\r\n");
      out.write("                                                        <th class=\"sorting\" tabindex=\"0\" aria-controls=\"example1\"\r\n");
      out.write("                                                            rowspan=\"1\" colspan=\"1\"\r\n");
      out.write("                                                            aria-label=\"Platform(s): activate to sort column ascending\">\r\n");
      out.write("                                                            性别\r\n");
      out.write("                                                        </th>\r\n");
      out.write("                                                        <th class=\"sorting\" tabindex=\"0\" aria-controls=\"example1\"\r\n");
      out.write("                                                            rowspan=\"1\" colspan=\"1\"\r\n");
      out.write("                                                            aria-label=\"CSS grade: activate to sort column ascending\">\r\n");
      out.write("                                                            班级\r\n");
      out.write("                                                        </th>\r\n");
      out.write("                                                        <th class=\"sorting\" tabindex=\"0\" aria-controls=\"example1\"\r\n");
      out.write("                                                            rowspan=\"1\" colspan=\"1\"\r\n");
      out.write("                                                            aria-label=\"CSS grade: activate to sort column ascending\">\r\n");
      out.write("                                                            科目\r\n");
      out.write("                                                        </th>\r\n");
      out.write("                                                        <th class=\"sorting\" tabindex=\"0\" aria-controls=\"example1\"\r\n");
      out.write("                                                            rowspan=\"1\" colspan=\"1\"\r\n");
      out.write("                                                            aria-label=\"CSS grade: activate to sort column ascending\">\r\n");
      out.write("                                                            成绩\r\n");
      out.write("                                                        </th>\r\n");
      out.write("                                                        <th class=\"sorting\" tabindex=\"0\" aria-controls=\"example1\"\r\n");
      out.write("                                                            rowspan=\"1\" colspan=\"1\"\r\n");
      out.write("                                                            aria-label=\"GPA: activate to sort column ascending\">\r\n");
      out.write("                                                            绩点\r\n");
      out.write("                                                        </th>\r\n");
      out.write("                                                        <th class=\"sorting\" tabindex=\"0\" aria-controls=\"example1\"\r\n");
      out.write("                                                            rowspan=\"1\" colspan=\"1\"\r\n");
      out.write("                                                            aria-label=\"Grade Level: activate to sort column ascending\">\r\n");
      out.write("                                                            等级\r\n");
      out.write("                                                        </th>\r\n");
      out.write("                                                        <th class=\"sorting\" tabindex=\"0\" aria-controls=\"example1\"\r\n");
      out.write("                                                            rowspan=\"1\" colspan=\"1\"\r\n");
      out.write("                                                            aria-label=\"CSS grade: activate to sort column ascending\">\r\n");
      out.write("                                                            专业\r\n");
      out.write("                                                        </th>\r\n");
      out.write("                                                        <th class=\"sorting\" tabindex=\"0\" aria-controls=\"example1\"\r\n");
      out.write("                                                            rowspan=\"1\" colspan=\"1\"\r\n");
      out.write("                                                            aria-label=\"CSS grade: activate to sort column ascending\">\r\n");
      out.write("                                                            操作\r\n");
      out.write("                                                        </th>\r\n");
      out.write("                                                    </tr>\r\n");
      out.write("                                                    </thead>\r\n");
      out.write("                                                    <tbody>\r\n");
      out.write("                                                    ");


                                                        int index = 0;
                                                        for (Score score : pager.getData()) {
                                                            index++;
                                                    
      out.write("\r\n");
      out.write("                                                    <tr class=\"");
      out.print( index % 2 == 1 ? "odd" : "even");
      out.write("\">\r\n");
      out.write("                                                        <td><input type=\"checkbox\" class=\"form-check\" name=\"checkItem\" value=\"");
      out.print( score.getScore_id());
      out.write("\"></td>\r\n");
      out.write("                                                        <td>");
      out.print( index);
      out.write("</td>\r\n");
      out.write("                                                        <td class=\"dtr-control sorting_1\"\r\n");
      out.write("                                                            tabindex=\"0\">");
      out.print( score.getStu_num());
      out.write("\r\n");
      out.write("                                                        </td>\r\n");
      out.write("                                                        <td>");
      out.print( score.getStu_name());
      out.write("\r\n");
      out.write("                                                        </td>\r\n");
      out.write("                                                        <td>");
      out.print( score.getStu_sex());
      out.write("\r\n");
      out.write("                                                        </td>\r\n");
      out.write("                                                        <td>");
      out.print( score.getStu_class());
      out.write("\r\n");
      out.write("                                                        </td>\r\n");
      out.write("                                                        <td>");
      out.print( score.getCourse_name());
      out.write("\r\n");
      out.write("                                                        </td>\r\n");
      out.write("                                                        <td>");
      out.print( score.getScore_grade());
      out.write("</td>\r\n");
      out.write("                                                        <td class=\"gpa-cell\">\r\n");
      out.write("                                                            <span class=\"gpa-value\">");
      out.print( String.format("%.1f", com.ntvu.studentmis.entity.Score.calculateGPA(score.getScore_grade())));
      out.write("</span>\r\n");
      out.write("                                                        </td>\r\n");
      out.write("                                                        <td class=\"grade-level-cell\">\r\n");
      out.write("                                                            <span class=\"grade-badge grade-");
      out.print( com.ntvu.studentmis.entity.Score.getGradeLevel(score.getScore_grade()).toLowerCase().replace("+", "plus").replace("-", "minus"));
      out.write("\">\r\n");
      out.write("                                                                ");
      out.print( com.ntvu.studentmis.entity.Score.getGradeLevel(score.getScore_grade()));
      out.write("\r\n");
      out.write("                                                            </span>\r\n");
      out.write("                                                        </td>\r\n");
      out.write("                                                        <td>");
      out.print( score.getMajor());
      out.write("</td>\r\n");
      out.write("                                                        <td style=\"width: 200px\">\r\n");
      out.write("                                                            <div class=\"row\">\r\n");
      out.write("                                                                <div class=\"col-6\">\r\n");
      out.write("                                                                    <button type=\"button\"\r\n");
      out.write("                                                                            class=\"btn btn-block btn-primary btn-xs\"\r\n");
      out.write("                                                                            style=\"width: 80px\" onclick=\"window.location.href = '");
      out.print( contextPath +"/admin/score/edit.jsp?id=" + score.getScore_id());
      out.write("';\">编辑\r\n");
      out.write("                                                                    </button>\r\n");
      out.write("                                                                </div>\r\n");
      out.write("                                                                <div class=\"col-6\">\r\n");
      out.write("                                                                    <button type=\"button\"\r\n");
      out.write("                                                                            class=\"btn btn-block btn-danger btn-xs\"\r\n");
      out.write("                                                                            style=\"width: 80px\" onclick=\"if(confirm('当前操作不可恢复，确认删除吗？')){\r\n");
      out.write("                                                                            window.location.href='");
      out.print( contextPath +"/admin/score/ScoreServlet?action=delete&id=" + score.getScore_id());
      out.write("';}\">删除\r\n");
      out.write("                                                                    </button>\r\n");
      out.write("                                                                </div>\r\n");
      out.write("                                                            </div>\r\n");
      out.write("                                                        </td>\r\n");
      out.write("                                                    </tr>\r\n");
      out.write("                                                    ");

                                                        }
                                                    
      out.write("\r\n");
      out.write("                                                    </tbody>\r\n");
      out.write("                                                </table>\r\n");
      out.write("                                            </div>\r\n");
      out.write("                                        </div>\r\n");
      out.write("                                        <div class=\"row\">\r\n");
      out.write("                                            <div class=\"col-sm-12 col-md-5\">\r\n");
      out.write("                                                <div class=\"dataTables_info\" id=\"example1_info\" role=\"status\"\r\n");
      out.write("                                                     aria-live=\"polite\">每页显示10条记录\r\n");
      out.write("                                                </div>\r\n");
      out.write("                                            </div>\r\n");
      out.write("                                            <div class=\"col-sm-12 col-md-7\">\r\n");
      out.write("                                                ");
      out.write("\r\n");
      out.write("总共<span style=\"color: red\">");
      out.print( pager.getRecordCount());
      out.write("</span>条，\r\n");
      out.write("每页显示<span style=\"color: red\">");
      out.print( pager.getPageSize());
      out.write("</span>条，\r\n");
      out.write("当前<span style=\"color: red\">");
      out.print( pager.getPageIndex());
      out.write("</span>/<span class=\"hint_blue\">");
      out.print( pager.getPageCount());
      out.write("</span>页，\r\n");
      out.write("<span style=\"text-decoration:underline; cursor: pointer\" onclick=\"doPager(1);\">首页</span>，\r\n");
      out.write("<span style=\"text-decoration:underline; cursor: pointer\" onclick=\"doPager(");
      out.print( pager.getPageIndex() > 1 ? pager.getPageIndex() - 1 : 1);
      out.write(");\">上一页</span>，\r\n");
      out.write("<span style=\"text-decoration:underline; cursor: pointer\" onclick=\"doPager(");
      out.print( pager.getPageIndex() >= pager.getPageCount() ? pager.getPageCount() :  pager.getPageIndex() + 1 );
      out.write(");\">下一页</span>，\r\n");
      out.write("<span style=\"text-decoration:underline; cursor: pointer\" onclick=\"doPager(");
      out.print( pager.getPageCount() );
      out.write(");\"  >末页</span>");
      out.write("\r\n");
      out.write("                                            </div>\r\n");
      out.write("                                        </div>\r\n");
      out.write("                                    </div>\r\n");
      out.write("                                </div>\r\n");
      out.write("                                <!-- /.card-body -->\r\n");
      out.write("                            </div>\r\n");
      out.write("                            <!-- /.card -->\r\n");
      out.write("                        </div>\r\n");
      out.write("                        <!-- /.col -->\r\n");
      out.write("                    </div>\r\n");
      out.write("                    <!-- /.row -->\r\n");
      out.write("                </div>\r\n");
      out.write("                <!-- /.container-fluid -->\r\n");
      out.write("            </section>\r\n");
      out.write("        </div>\r\n");
      out.write("    </form>\r\n");
      out.write("</div>\r\n");
      out.write("\r\n");
      out.write("\r\n");
      out.write("<!-- jQuery -->\r\n");
      out.write("<script src=\"");
      out.print( request.getContextPath());
      out.write("/plugins/jquery/jquery.min.js\"></script>\r\n");
      out.write("<!-- jQuery UI 1.11.4 -->\r\n");
      out.write("<script src=\"");
      out.print( request.getContextPath());
      out.write("/plugins/jquery-ui/jquery-ui.min.js\"></script>\r\n");
      out.write("<!-- Resolve conflict in jQuery UI tooltip with Bootstrap tooltip -->\r\n");
      out.write("<script>\r\n");
      out.write("    $.widget.bridge('uibutton', $.ui.button)\r\n");
      out.write("</script>\r\n");
      out.write("\r\n");
      out.write("<!-- Bootstrap 4 -->\r\n");
      out.write("<script src=\"");
      out.print( request.getContextPath());
      out.write("/plugins/bootstrap/js/bootstrap.bundle.min.js\"></script>\r\n");
      out.write("<!-- ChartJS -->\r\n");
      out.write("<script src=\"");
      out.print( request.getContextPath());
      out.write("/plugins/chart.js/Chart.min.js\"></script>\r\n");
      out.write("<!-- Sparkline -->\r\n");
      out.write("<script src=\"");
      out.print( request.getContextPath());
      out.write("/plugins/sparklines/sparkline.js\"></script>\r\n");
      out.write("<!-- JQVMap -->\r\n");
      out.write("<script src=\"");
      out.print( request.getContextPath());
      out.write("/plugins/jqvmap/jquery.vmap.min.js\"></script>\r\n");
      out.write("<script src=\"");
      out.print( request.getContextPath());
      out.write("/plugins/jqvmap/maps/jquery.vmap.usa.js\"></script>\r\n");
      out.write("<!-- jQuery Knob Chart -->\r\n");
      out.write("<script src=\"");
      out.print( request.getContextPath());
      out.write("/plugins/jquery-knob/jquery.knob.min.js\"></script>\r\n");
      out.write("<!-- daterangepicker -->\r\n");
      out.write("<script src=\"");
      out.print( request.getContextPath());
      out.write("/plugins/moment/moment.min.js\"></script>\r\n");
      out.write("<script src=\"");
      out.print( request.getContextPath());
      out.write("/plugins/daterangepicker/daterangepicker.js\"></script>\r\n");
      out.write("<!-- Tempusdominus Bootstrap 4 -->\r\n");
      out.write("<script src=\"");
      out.print( request.getContextPath());
      out.write("/plugins/tempusdominus-bootstrap-4/js/tempusdominus-bootstrap-4.min.js\"></script>\r\n");
      out.write("<!-- Summernote -->\r\n");
      out.write("<script src=\"");
      out.print( request.getContextPath());
      out.write("/plugins/summernote/summernote-bs4.min.js\"></script>\r\n");
      out.write("<!-- overlayScrollbars -->\r\n");
      out.write("<script src=\"");
      out.print( request.getContextPath());
      out.write("/plugins/overlayScrollbars/js/jquery.overlayScrollbars.min.js\"></script>\r\n");
      out.write("<!-- AdminLTE App -->\r\n");
      out.write("<script src=\"");
      out.print( request.getContextPath());
      out.write("/dist/js/adminlte.js\"></script>\r\n");
      out.write("<!-- AdminLTE for demo purposes -->\r\n");
      out.write("<script src=\"");
      out.print( request.getContextPath());
      out.write("/dist/js/demo.js\"></script>\r\n");
      out.write("<!-- AdminLTE dashboard demo (This is only for demo purposes) -->\r\n");
      out.write("<script src=\"");
      out.print( request.getContextPath());
      out.write("/dist/js/pages/dashboard.js\"></script>\r\n");
      out.write("<script type=\"text/javascript\" src=\"");
      out.print( request.getContextPath());
      out.write("/js/jquery-3.6.1.min.js\"></script>\r\n");
      out.write("<script type=\"text/javascript\" src=\"");
      out.print( request.getContextPath());
      out.write("/js/jquery.min.js\"></script>");
      out.write("\r\n");
      out.write("<style>\r\n");
      out.write("    /* GPA和等级显示样式 */\r\n");
      out.write("    .gpa-cell {\r\n");
      out.write("        text-align: center;\r\n");
      out.write("        font-weight: bold;\r\n");
      out.write("    }\r\n");
      out.write("\r\n");
      out.write("    .gpa-value {\r\n");
      out.write("        color: #007bff;\r\n");
      out.write("        font-size: 1.1em;\r\n");
      out.write("    }\r\n");
      out.write("\r\n");
      out.write("    .grade-level-cell {\r\n");
      out.write("        text-align: center;\r\n");
      out.write("    }\r\n");
      out.write("\r\n");
      out.write("    .grade-badge {\r\n");
      out.write("        display: inline-block;\r\n");
      out.write("        padding: 4px 8px;\r\n");
      out.write("        border-radius: 12px;\r\n");
      out.write("        font-size: 0.85em;\r\n");
      out.write("        font-weight: bold;\r\n");
      out.write("        text-align: center;\r\n");
      out.write("        min-width: 35px;\r\n");
      out.write("    }\r\n");
      out.write("\r\n");
      out.write("    /* 等级颜色 */\r\n");
      out.write("    .grade-a {\r\n");
      out.write("        background-color: #28a745;\r\n");
      out.write("        color: white;\r\n");
      out.write("    }\r\n");
      out.write("\r\n");
      out.write("    .grade-aminus {\r\n");
      out.write("        background-color: #20c997;\r\n");
      out.write("        color: white;\r\n");
      out.write("    }\r\n");
      out.write("\r\n");
      out.write("    .grade-bplus {\r\n");
      out.write("        background-color: #17a2b8;\r\n");
      out.write("        color: white;\r\n");
      out.write("    }\r\n");
      out.write("\r\n");
      out.write("    .grade-b {\r\n");
      out.write("        background-color: #6f42c1;\r\n");
      out.write("        color: white;\r\n");
      out.write("    }\r\n");
      out.write("\r\n");
      out.write("    .grade-bminus {\r\n");
      out.write("        background-color: #6610f2;\r\n");
      out.write("        color: white;\r\n");
      out.write("    }\r\n");
      out.write("\r\n");
      out.write("    .grade-cplus {\r\n");
      out.write("        background-color: #fd7e14;\r\n");
      out.write("        color: white;\r\n");
      out.write("    }\r\n");
      out.write("\r\n");
      out.write("    .grade-c {\r\n");
      out.write("        background-color: #ffc107;\r\n");
      out.write("        color: #212529;\r\n");
      out.write("    }\r\n");
      out.write("\r\n");
      out.write("    .grade-cminus {\r\n");
      out.write("        background-color: #e83e8c;\r\n");
      out.write("        color: white;\r\n");
      out.write("    }\r\n");
      out.write("\r\n");
      out.write("    .grade-d {\r\n");
      out.write("        background-color: #dc3545;\r\n");
      out.write("        color: white;\r\n");
      out.write("    }\r\n");
      out.write("\r\n");
      out.write("    .grade-f {\r\n");
      out.write("        background-color: #6c757d;\r\n");
      out.write("        color: white;\r\n");
      out.write("    }\r\n");
      out.write("</style>\r\n");
      out.write("<script>\r\n");
      out.write("    $(function (){\r\n");
      out.write("        //绑定勾选框按钮事件\r\n");
      out.write("        $('input[name=checkAll]').bind('change',function (){\r\n");
      out.write("            console.log('checkAll');\r\n");
      out.write("            let checked=$(this).prop('checked');\r\n");
      out.write("            //更改表格中所有chkItem\r\n");
      out.write("            $('input[name=checkItem]').each(function (){\r\n");
      out.write("                console.log('checkItem');\r\n");
      out.write("                $(this).prop('checked',checked);\r\n");
      out.write("            });\r\n");
      out.write("        });\r\n");
      out.write("        //绑定删除所有按钮事件\r\n");
      out.write("        $('input[name=btnDelSel]').bind('click',function (){\r\n");
      out.write("            let ids='';\r\n");
      out.write("            $('input[name=checkItem]').each(function (){\r\n");
      out.write("                if( $(this).prop('checked')===true)\r\n");
      out.write("                {\r\n");
      out.write("                    ids+=$(this).val()+',';\r\n");
      out.write("                }\r\n");
      out.write("            });\r\n");
      out.write("            if(ids.length>0)\r\n");
      out.write("            {\r\n");
      out.write("                if(confirm('当前操作不可恢复,确认要删除吗?'))\r\n");
      out.write("                {\r\n");
      out.write("                    console.log(ids);\r\n");
      out.write("                    window.location.href='");
      out.print(contextPath+"/admin/score/ScoreServlet?action=deleteSelected&ids=");
      out.write("'+ids;\r\n");
      out.write("                }\r\n");
      out.write("            }else {\r\n");
      out.write("                alert('请选择待删除项');\r\n");
      out.write("            }\r\n");
      out.write("\r\n");
      out.write("        });\r\n");
      out.write("        $('input[name=btnFind]').bind('click',function (){\r\n");
      out.write("            $('#form1').attr('action','");
      out.print( request.getContextPath() + "/admin/score/list.jsp");
      out.write("');\r\n");
      out.write("            $(`#form1`).submit();\r\n");
      out.write("        });\r\n");
      out.write("    });\r\n");
      out.write("    /**\r\n");
      out.write("     * 跳转到指定的页\r\n");
      out.write("     * @param toPageIndex\r\n");
      out.write("     */\r\n");
      out.write("        //本页地址\r\n");
      out.write("    let pageListUrl = '/admin/score/list.jsp';\r\n");
      out.write("    function doPager(toPageIndex)\r\n");
      out.write("    {\r\n");
      out.write("        $('#form1').attr('action','");
      out.print( request.getContextPath() );
      out.write("' + pageListUrl + '?pageIndex=' + toPageIndex);\r\n");
      out.write("        $('#form1').submit();\r\n");
      out.write("    }\r\n");
      out.write("</script>\r\n");
      out.write("\r\n");
      out.write("</body>\r\n");
      out.write("</html>\r\n");
    } catch (java.lang.Throwable t) {
      if (!(t instanceof javax.servlet.jsp.SkipPageException)){
        out = _jspx_out;
        if (out != null && out.getBufferSize() != 0)
          try { out.clearBuffer(); } catch (java.io.IOException e) {}
        if (_jspx_page_context != null) _jspx_page_context.handlePageException(t);
        else throw new ServletException(t);
      }
    } finally {
      _jspxFactory.releasePageContext(_jspx_page_context);
    }
  }
}
