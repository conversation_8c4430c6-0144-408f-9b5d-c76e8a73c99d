package com.ntvu.studentmis.db;

import com.ntvu.studentmis.entity.User;
import com.ntvu.studentmis.pager.PagerHelper;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;

/**
 * 对数据库操作的类
 */
public class DBManager {
    //驱动类
    private final String driverClassName = "com.mysql.cj.jdbc.Driver";
    //连接数据库地址
    private final String url = "*****************************************";
    //连接数据库用户名
    private final String dbName = "root";
    //连接数据库密码
    private final String dbPwd = "xhxabc";

    //
    private Connection conn = null;
    private Statement stmt = null;
    private String sql = null;
    private ResultSet rs = null;

    /**
     * 初始化数据连接
     */
    private void init(){
        try {
            //加载驱动
            Class.forName(driverClassName);
            //获得与数据库的连接
            this.conn = DriverManager.getConnection(url,dbName,dbPwd);
            //获得招待句柄
            this.stmt = this.conn.createStatement();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    /**
     * 释放连接资源
     */
    private void release()
    {
        //关闭
        try {
            if(rs != null && !rs.isClosed())
            {
                rs.close();
                rs = null;
            }
            if(stmt != null && !stmt.isClosed()) {
                stmt.close();
                stmt = null;
            }
            if(conn != null && !conn.isClosed()) {
                conn.close();
                conn = null;
            }
        } catch (SQLException throwables) {
            throwables.printStackTrace();
        }
    }

    public boolean login(String user_num,String password)
    {
        boolean succeed = false;
        try {
            init();
            //定义sql
            sql = "select * from user where user_num ='%s' and password = '%s'";
            sql = String.format(sql,user_num,password);
            //执行，获得返回结果
            System.out.println(sql);
            rs = stmt.executeQuery(sql);
            //使用结果
            if(rs.next())
            {
                //如果结果集不为空
                succeed = true;
            }else{
                succeed = false;
            }
            release();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return succeed;
    }

    public boolean register(User user)
    {
        boolean succeed = false;
        try{
            init();
            sql = "insert into user(user_num,user_name,password,phone,role_id)" +
                    " values(%s,%s,%s,%s,%d)";
            sql = String.format(sql,
                    "'" + user.getUser_num() + "'",
                    "'" + user.getUser_name() + "'",
                    "'" + user.getPassword() + "'",
                    "'" + user.getPhone() + "'",
                    user.getRole_id());
            System.out.println(sql);
            //执行，返回受影响的记录行数
            int effectedRows = stmt.executeUpdate(sql);
            //使用结果
            //如果结果集不为空
            succeed = effectedRows > 0;
            //关闭
            release();
        }catch (Exception ex)
        {
            ex.printStackTrace();
        }
        return succeed;
    }

    public boolean delete(int id)
    {
        boolean succeed = false;
        try{
            init();
            sql = "delete from user where user_id = %d";
            sql = String.format(sql, id);
            System.out.println(sql);
            //执行，返回受影响的记录行数
            int effectedRows = stmt.executeUpdate(sql);
            //使用结果
            if(effectedRows > 0)
            {
                //如果结果集不为空
                succeed = true;
            }else{
                succeed = false;
            }
            //关闭
            release();
        }catch (Exception ex)
        {
            ex.printStackTrace();
        }
        return succeed;
    }

    public boolean edit(User user)
    {
        boolean succeed = false;
        try{
            init();
            sql = "update user set user_num=%s,user_name=%s,password=%s,phone=%s,role_id=%d where user_id = %d";
            sql = String.format(sql,
                    "'" + user.getUser_num() + "'",
                    "'" + user.getUser_name() + "'",
                    "'" + user.getPassword() + "'",
                    "'" + user.getPhone() + "'",
                    user.getRole_id(),
                    user.getUser_id());
            System.out.println(sql);
            //执行，返回受影响的记录行数
            int effectedRows = stmt.executeUpdate(sql);
            //使用结果
            //如果结果集不为空
            succeed = effectedRows > 0;
            //关闭
            release();
        }catch (Exception ex)
        {
            ex.printStackTrace();
        }
        return succeed;
    }
    //编辑删除
    public User getDetails(int id)
    {
        User user = null;
        try {
            init();
            sql = "select * from user where user_id = %d";
            sql = String.format(sql,id);
            //执行，获得返回结果
            System.out.println(sql);
            ResultSet rs = stmt.executeQuery(sql);
            //使用结果
            if(rs.next())
            {
                //如果结果集不为空
                user = new User();
                user.setUser_id(rs.getInt("user_id"));
                user.setUser_num(rs.getString("user_num"));
                user.setUser_name(rs.getString("user_name"));
                user.setPassword(rs.getString("password"));
                user.setPhone(rs.getString("phone"));
                user.setRole_id(rs.getInt("role_id"));
            }
            //关闭
            release();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return user;
    }
    //根据用户名查找密码
    public User getDetails(String user_num)
    {
        User user = null;
        try {
            init();
            sql = "select * from user where user_num = '%s'";
            sql = String.format(sql,user_num);
            //执行，获得返回结果
            System.out.println(sql);
            ResultSet rs = stmt.executeQuery(sql);
            //使用结果
            if(rs.next())
            {
                //如果结果集不为空
                user = new User();
                user.setUser_id(rs.getInt("user_id"));
                user.setUser_num(rs.getString("user_num"));
                user.setUser_name(rs.getString("user_name"));
                user.setPassword(rs.getString("password"));
                user.setPhone(rs.getString("phone"));
                user.setRole_id(rs.getInt("role_id"));
            }
            //关闭
            release();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return user;
    }

    public int getRecordCount(String user_num, String user_name)
    {
        int count = 0;
        try {
            init();
            sql = "select count(*) from user where 1 = 1 ";
            if(user_num != null && !user_num.equals(""))
            {
                sql += " and user_num like '%" + user_num + "%'";
            }
            if(user_name != null && !user_name.equals(""))
            {
                sql += " and user_name like '%" + user_name + "%'";
            }
            //执行，获得返回结果
            System.out.println(sql);
            ResultSet rs = stmt.executeQuery(sql);
            //使用结果
            if(rs.next())
            {
                count = rs.getInt(1);
            }
            //关闭
            release();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return count;
    }

    public void getList(PagerHelper<User> pager)
    {
        try {
            init();
            sql = "select * from user where 1 = 1 ";
            if(pager.getQueryParams().containsKey("user_num"))
            {
                sql += " and user_num like '%" + pager.getQueryParams().get("user_num") + "%'";
            }
            if(pager.getQueryParams().containsKey("user_name"))
            {
                sql += " and user_name like '%" + pager.getQueryParams().get("user_name") + "%'";
            }


            //拼接：limit
            sql += " limit " + (pager.getPageIndex() - 1) * pager.getPageSize() + "," + pager.getPageSize();//limit 3,3

            //执行，获得返回结果
            System.out.println(sql);
            ResultSet rs = stmt.executeQuery(sql);
            //使用结果
            List<User> lst = new ArrayList<>();
            while(rs.next())
            {
                //如果结果集不为空
                User user = new User();
                user.setUser_id(rs.getInt("user_id"));
                user.setUser_num(rs.getString("user_num"));
                user.setUser_name(rs.getString("user_name"));
                user.setPassword(rs.getString("password"));
                user.setPhone(rs.getString("phone"));
                user.setRole_id(rs.getInt("role_id"));
                lst.add(user);
            }
            pager.setData(lst);
            //关闭
            release();
            //
            int count = getRecordCount(pager.getQueryParams().get("user_num"),pager.getQueryParams().get("user_name"));
            pager.setRecordCount(count);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
