<%@ page import="com.ntvu.studentmis.pager.PagerHelper" %>
<%@ page import="com.ntvu.studentmis.entity.Score" %>
<%@ page import="com.ntvu.studentmis.util.WebTools" %>
<%@ page import="com.ntvu.studentmis.db.DBScore" %>
<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <jsp:include page="../include/header_css.jsp" flush="true"/>
    <title>学生成绩列表</title>
</head>
<body class="hold-transition sidebar-mini layout-fixed">
<div class="wrapper">
    <%@include file="header_nav.jsp"%>
    <%@include file="left_menu.jsp"%>
    <%
        request.setCharacterEncoding("utf-8");
        PagerHelper<Score> pager = new PagerHelper(request);
        if (g_user.getUser_num() != null && !g_user.getUser_num().trim().equals("")) {
            pager.getQueryParams().put("stu_num", g_user.getUser_num());
        }
        if (g_user.getUser_name() != null && !g_user.getUser_name().trim().equals("")) {
            pager.getQueryParams().put("stu_name", g_user.getUser_name());
        }
        new DBScore().getList(pager);
        double sumScore=pager.getSumScore();
        double avgScore=pager.getAvgScore();
    %>
    <!-- Content Wrapper. Contains page content -->
    <form id="form1" name="form1" method="post"
          action="<%= request.getContextPath() + "/admin/student/score.jsp"%>">
        <div class="content-wrapper">
            <section class="content">
                <div class="container-fluid">
                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <div class="row">
                                        <div class="col-lg-2">此学生总成绩：<input type="text" class="form-control" disabled="disabled" value="<%= sumScore==0.0?"":sumScore%>" placeholder="点击查询后显示"></div>
                                        <div class="col-lg-2">此学生平均分：<input type="text" class="form-control" disabled="disabled" value="<%= avgScore==0.0?"":avgScore%>" placeholder="点击查询后显示"></div>
                                    </div>
                                </div>

                                <!-- 学习进度图表区域 -->
                                <div class="card-body">
                                    <div class="row mb-4">
                                        <div class="col-md-6">
                                            <div class="card">
                                                <div class="card-header">
                                                    <h3 class="card-title">
                                                        <i class="fas fa-chart-pie mr-1"></i>
                                                        当前学习进度
                                                    </h3>
                                                </div>
                                                <div class="card-body">
                                                    <canvas id="currentProgressChart" style="height: 250px;"></canvas>
                                                    <div class="mt-3">
                                                        <div class="progress-group">
                                                            已完成学分：<span id="completedCredits" class="float-right"><b>0</b>/160</span>
                                                            <div class="progress progress-sm">
                                                                <div id="creditProgressBar" class="progress-bar bg-primary" style="width: 0%"></div>
                                                            </div>
                                                        </div>
                                                        <div class="progress-group">
                                                            当前GPA：<span id="currentGPA" class="float-right"><b>0.0</b>/4.0</span>
                                                            <div class="progress progress-sm">
                                                                <div id="gpaProgressBar" class="progress-bar bg-success" style="width: 0%"></div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="card">
                                                <div class="card-header">
                                                    <h3 class="card-title">
                                                        <i class="fas fa-chart-bar mr-1"></i>
                                                        毕业进度分析
                                                    </h3>
                                                </div>
                                                <div class="card-body">
                                                    <canvas id="graduationProgressChart" style="height: 250px;"></canvas>
                                                    <div class="mt-3">
                                                        <div class="info-box">
                                                            <span class="info-box-icon bg-info"><i class="fas fa-graduation-cap"></i></span>
                                                            <div class="info-box-content">
                                                                <span class="info-box-text">毕业进度</span>
                                                                <span id="graduationPercentage" class="info-box-number">0%</span>
                                                                <div class="progress">
                                                                    <div id="graduationProgressBar" class="progress-bar bg-info" style="width: 0%"></div>
                                                                </div>
                                                                <span id="graduationStatus" class="progress-description">距离毕业还需努力</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 成绩表格区域 -->
                                <div class="card-body">
                                        <div class="row">
                                            <div class="col-sm-12">
                                                <table id="example1"
                                                       class="table table-bordered table-striped dataTable dtr-inline"
                                                       aria-describedby="example1_info">
                                                    <thead>
                                                    <tr>
                                                        <th class="sorting" tabindex="0" aria-controls="example1"
                                                            rowspan="1" colspan="1"
                                                            aria-label="Platform(s): activate to sort column ascending">
                                                            序号
                                                        </th>
                                                        <th class="sorting" tabindex="0" aria-controls="example1"
                                                            rowspan="1" colspan="1"
                                                            aria-label="Browser: activate to sort column ascending">
                                                            学号
                                                        </th>
                                                        <th class="sorting" tabindex="0" aria-controls="example1"
                                                            rowspan="1" colspan="1"
                                                            aria-label="Platform(s): activate to sort column ascending">
                                                            姓名
                                                        </th>
                                                        <th class="sorting" tabindex="0" aria-controls="example1"
                                                            rowspan="1" colspan="1"
                                                            aria-label="CSS grade: activate to sort column ascending">
                                                            班级
                                                        </th>
                                                        <th class="sorting" tabindex="0" aria-controls="example1"
                                                            rowspan="1" colspan="1"
                                                            aria-label="CSS grade: activate to sort column ascending">
                                                            科目
                                                        </th>
                                                        <th class="sorting" tabindex="0" aria-controls="example1"
                                                            rowspan="1" colspan="1"
                                                            aria-label="CSS grade: activate to sort column ascending">
                                                            成绩
                                                        </th>
                                                        <th class="sorting" tabindex="0" aria-controls="example1"
                                                            rowspan="1" colspan="1"
                                                            aria-label="CSS grade: activate to sort column ascending">
                                                            专业
                                                        </th>
                                                    </tr>
                                                    </thead>
                                                    <tbody>
                                                    <%
                                                        int index = 0;
                                                        for (Score score : pager.getData()) {
                                                            index++;
                                                    %>
                                                    <tr class="<%= index % 2 == 1 ? "odd" : "even"%>">
                                                        <td><%= index%></td>
                                                        <td class="dtr-control sorting_1"
                                                            tabindex="0"><%= score.getStu_num()%>
                                                        </td>
                                                        <td><%= score.getStu_name()%>
                                                        </td>
                                                        <td><%= score.getStu_class()%>
                                                        </td>
                                                        <td><%= score.getCourse_name()%>
                                                        </td>
                                                        <td><%= score.getScore_grade()%></td>
                                                        <td><%= score.getMajor()%></td>
                                                    </tr>
                                                    <%
                                                        }
                                                    %>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-12 col-md-5">
                                                <div class="dataTables_info" id="example1_info" role="status"
                                                     aria-live="polite">每页显示10条记录
                                                </div>
                                            </div>
                                            <div class="col-sm-12 col-md-7">
                                                <%@ include file="../include/pager_footer.jsp"%>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- /.card-body -->
                            </div>
                            <!-- /.card -->
                        </div>
                        <!-- /.col -->
                    </div>
                    <!-- /.row -->
                </div>
                <!-- /.container-fluid -->
            </section>
        </div>
    </form>
</div>
<%@include file="../include/foot_js.jsp"%>
<script>
    //本页地址
    let pageListUrl = '/student/score.jsp';
    function doPager(toPageIndex)
    {
        $('#form1').attr('action','<%= request.getContextPath() %>' + pageListUrl + '?pageIndex=' + toPageIndex);
        $('#form1').submit();
    }

    // 学习进度数据计算和图表渲染
    $(document).ready(function() {
        // 获取成绩数据
        var scoreData = [];
        <%
        for (Score score : pager.getData()) {
        %>
        scoreData.push({
            stuNum: '<%= score.getStu_num()%>',
            stuName: '<%= score.getStu_name()%>',
            courseName: '<%= score.getCourse_name()%>',
            score: <%= score.getScore_grade()%>,
            major: '<%= score.getMajor()%>'
        });
        <%
        }
        %>

        // 计算学习进度数据
        var progressData = calculateProgressData(scoreData);

        // 渲染图表
        renderCurrentProgressChart(progressData);
        renderGraduationProgressChart(progressData);

        // 更新进度显示
        updateProgressDisplay(progressData);
    });

    // 计算进度数据
    function calculateProgressData(scoreData) {
        var totalCredits = 160; // 毕业要求总学分
        var totalCourses = 40;   // 毕业要求总课程数
        var minGPA = 2.0;        // 最低GPA要求

        // 计算已完成学分（假设每门课程3学分，及格分60分）
        var completedCredits = 0;
        var totalScore = 0;
        var passedCourses = 0;
        var failedCourses = 0;

        scoreData.forEach(function(score) {
            var credits = 3; // 假设每门课程3学分
            totalScore += score.score;

            if (score.score >= 60) {
                completedCredits += credits;
                passedCourses++;
            } else {
                failedCourses++;
            }
        });

        var avgScore = scoreData.length > 0 ? totalScore / scoreData.length : 0;
        var currentGPA = convertScoreToGPA(avgScore);

        // 计算各项进度百分比
        var creditProgress = Math.min(100, (completedCredits / totalCredits) * 100);
        var gpaProgress = Math.min(100, (currentGPA / 4.0) * 100);
        var courseProgress = Math.min(100, (passedCourses / totalCourses) * 100);

        // 综合毕业进度
        var graduationProgress = Math.round(
            creditProgress * 0.4 +    // 学分完成度 40%
            gpaProgress * 0.3 +       // GPA达标度 30%
            courseProgress * 0.2 +    // 课程完成度 20%
            Math.max(0, 100 - failedCourses * 5) * 0.1  // 挂科控制 10%
        );

        return {
            totalCredits: totalCredits,
            completedCredits: completedCredits,
            remainingCredits: totalCredits - completedCredits,
            currentGPA: currentGPA,
            avgScore: avgScore,
            passedCourses: passedCourses,
            failedCourses: failedCourses,
            creditProgress: creditProgress,
            gpaProgress: gpaProgress,
            courseProgress: courseProgress,
            graduationProgress: Math.min(100, graduationProgress)
        };
    }

    // 分数转GPA
    function convertScoreToGPA(score) {
        if (score >= 90) return 4.0;
        if (score >= 85) return 3.7;
        if (score >= 82) return 3.3;
        if (score >= 78) return 3.0;
        if (score >= 75) return 2.7;
        if (score >= 72) return 2.3;
        if (score >= 68) return 2.0;
        if (score >= 64) return 1.7;
        if (score >= 60) return 1.0;
        return 0.0;
    }

    // 渲染当前进度饼图
    function renderCurrentProgressChart(data) {
        var ctx = document.getElementById('currentProgressChart').getContext('2d');

        new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['已完成学分', '剩余学分'],
                datasets: [{
                    data: [data.completedCredits, data.remainingCredits],
                    backgroundColor: ['#28a745', '#dc3545'],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                legend: {
                    position: 'bottom'
                },
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                var label = context.label || '';
                                var value = context.parsed;
                                var percentage = ((value / data.totalCredits) * 100).toFixed(1);
                                return label + ': ' + value + '学分 (' + percentage + '%)';
                            }
                        }
                    }
                },
                cutout: '60%'
            }
        });
    }

    // 渲染毕业进度柱状图
    function renderGraduationProgressChart(data) {
        var ctx = document.getElementById('graduationProgressChart').getContext('2d');

        new Chart(ctx, {
            type: 'bar',
            data: {
                labels: ['学分进度', 'GPA进度', '课程进度', '综合进度'],
                datasets: [{
                    label: '完成百分比',
                    data: [data.creditProgress, data.gpaProgress, data.courseProgress, data.graduationProgress],
                    backgroundColor: [
                        '#007bff',
                        '#28a745',
                        '#ffc107',
                        '#17a2b8'
                    ],
                    borderColor: [
                        '#0056b3',
                        '#1e7e34',
                        '#e0a800',
                        '#117a8b'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                legend: {
                    display: false
                },
                scales: {
                    yAxes: [{
                        ticks: {
                            beginAtZero: true,
                            max: 100,
                            callback: function(value) {
                                return value + '%';
                            }
                        }
                    }]
                },
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return context.dataset.label + ': ' + context.parsed.y.toFixed(1) + '%';
                            }
                        }
                    }
                }
            }
        });
    }

    // 更新进度显示
    function updateProgressDisplay(data) {
        // 更新学分进度
        $('#completedCredits').html('<b>' + data.completedCredits + '</b>/' + data.totalCredits);
        $('#creditProgressBar').css('width', data.creditProgress + '%');

        // 更新GPA进度
        $('#currentGPA').html('<b>' + data.currentGPA.toFixed(1) + '</b>/4.0');
        $('#gpaProgressBar').css('width', data.gpaProgress + '%');

        // 更新毕业进度
        $('#graduationPercentage').text(data.graduationProgress + '%');
        $('#graduationProgressBar').css('width', data.graduationProgress + '%');

        // 更新毕业状态文字
        var status = '';
        if (data.graduationProgress >= 90) {
            status = '即将毕业，恭喜！';
        } else if (data.graduationProgress >= 75) {
            status = '进度良好，继续努力！';
        } else if (data.graduationProgress >= 50) {
            status = '进度正常，需要加油！';
        } else {
            status = '距离毕业还需努力！';
        }
        $('#graduationStatus').text(status);
    }
</script>

</body>
</html>
