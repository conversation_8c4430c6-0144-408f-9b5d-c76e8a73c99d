-- StudentMIS 50个真实用户数据更新脚本
-- 使用数据库
USE studentmis_db;

-- 备份现有数据（可选）
-- CREATE TABLE user_backup AS SELECT * FROM user;

-- 清空现有用户数据
DELETE FROM user;

-- 重置自增ID
ALTER TABLE user AUTO_INCREMENT = 1;

-- 插入50个真实用户数据
-- 学号格式：10位数字（年份+专业代码+序号）
-- 密码：与学号保持一致
-- 角色：0=学生，1=教师，2=管理员

INSERT INTO user (user_num, user_name, password, phone, role_id) VALUES
-- 超级管理员（徐浩翔）
('2024140520', '徐浩翔', '2024140520', '18626425051', 2),

-- 教师账号（7个）
('2024000001', '张明华', '2024000001', '13912345678', 1),
('2024000002', '李晓芳', '2024000002', '13823456789', 1),
('2024000003', '王建国', '2024000003', '13734567890', 1),
('2024000004', '陈美丽', '2024000004', '13645678901', 1),
('2024000005', '刘志强', '2024000005', '13556789012', 1),
('2024000006', '赵雅琴', '2024000006', '13467890123', 1),
('2024000007', '孙德华', '2024000007', '13378901234', 1),

-- 学生账号（42个）
-- 计算机科学与技术专业（140开头）
('2024140501', '马浩然', '2024140501', '13289012345', 0),
('2024140502', '李思雨', '2024140502', '13190123456', 0),
('2024140503', '张志远', '2024140503', '13801234567', 0),
('2024140504', '王雪梅', '2024140504', '13712345678', 0),
('2024140505', '陈俊杰', '2024140505', '13623456789', 0),
('2024140506', '刘美玲', '2024140506', '13534567890', 0),
('2024140507', '杨文博', '2024140507', '13445678901', 0),
('2024140508', '赵晓燕', '2024140508', '13356789012', 0),
('2024140509', '孙志明', '2024140509', '13267890123', 0),
('2024140510', '周雅婷', '2024140510', '13178901234', 0),
('2024140511', '吴建华', '2024140511', '13089012345', 0),
('2024140512', '郑丽娜', '2024140512', '13990123456', 0),
('2024140513', '冯浩宇', '2024140513', '13891234567', 0),
('2024140514', '何晓敏', '2024140514', '13792345678', 0),
('2024140515', '许志强', '2024140515', '13693456789', 0),
('2024140516', '蒋美华', '2024140516', '13594567890', 0),
('2024140517', '韩文杰', '2024140517', '13495678901', 0),
('2024140518', '曹雪芳', '2024140518', '13396789012', 0),
('2024140519', '邓志远', '2024140519', '13297890123', 0),
('2024140521', '谢晓丽', '2024140521', '13198901234', 0),
('2024140522', '罗建国', '2024140522', '13099012345', 0),
('2024140523', '梁美玲', '2024140523', '13900123456', 0),
('2024140524', '宋志华', '2024140524', '13801234568', 0),
('2024140525', '唐雅琴', '2024140525', '13702345679', 0),

-- 软件工程专业（141开头）
('2024141001', '高文博', '2024141001', '13603456780', 0),
('2024141002', '林晓燕', '2024141002', '13504567891', 0),
('2024141003', '胡志明', '2024141003', '13405678902', 0),
('2024141004', '钟雅婷', '2024141004', '13306789013', 0),
('2024141005', '叶建华', '2024141005', '13207890124', 0),
('2024141006', '苏丽娜', '2024141006', '13108901235', 0),
('2024141007', '潘浩宇', '2024141007', '13009012346', 0),
('2024141008', '董晓敏', '2024141008', '13910123457', 0),
('2024141009', '薛志强', '2024141009', '13811234568', 0),
('2024141010', '范美华', '2024141010', '13712345679', 0),

-- 信息管理与信息系统专业（142开头）
('2024142001', '石文杰', '2024142001', '13613456780', 0),
('2024142002', '姚雪芳', '2024142002', '13514567891', 0),
('2024142003', '汪志远', '2024142003', '13415678902', 0),
('2024142004', '金晓丽', '2024142004', '13316789013', 0),
('2024142005', '段建国', '2024142005', '13217890124', 0),
('2024142006', '白美玲', '2024142006', '13118901235', 0),
('2024142007', '康志华', '2024142007', '13019012346', 0),
('2024142008', '毛雅琴', '2024142008', '13920123457', 0),

-- 数据科学与大数据技术专业（143开头）
('2024143001', '严文博', '2024143001', '13821234568', 0),
('2024143002', '尹晓燕', '2024143002', '13722345679', 0),
('2024143003', '常志明', '2024143003', '13623456780', 0),
('2024143004', '武雅婷', '2024143004', '13524567891', 0);

-- 验证插入结果
SELECT 
    user_num AS '学号',
    user_name AS '姓名', 
    password AS '密码',
    phone AS '手机号',
    CASE role_id 
        WHEN 0 THEN '学生'
        WHEN 1 THEN '教师' 
        WHEN 2 THEN '管理员'
        ELSE '未知'
    END AS '角色'
FROM user 
ORDER BY role_id DESC, user_num;

-- 显示统计信息
SELECT 
    COUNT(*) AS '总用户数',
    SUM(CASE WHEN role_id = 2 THEN 1 ELSE 0 END) AS '管理员数',
    SUM(CASE WHEN role_id = 1 THEN 1 ELSE 0 END) AS '教师数',
    SUM(CASE WHEN role_id = 0 THEN 1 ELSE 0 END) AS '学生数'
FROM user;

-- 验证学号格式（应该都是10位）
SELECT 
    user_num,
    LENGTH(user_num) AS '学号长度',
    CASE 
        WHEN LENGTH(user_num) = 10 THEN '✓ 正确'
        ELSE '✗ 错误'
    END AS '格式检查'
FROM user
WHERE LENGTH(user_num) != 10;

-- 验证密码与学号一致性
SELECT 
    user_num,
    password,
    CASE 
        WHEN user_num = password THEN '✓ 一致'
        ELSE '✗ 不一致'
    END AS '密码检查'
FROM user
WHERE user_num != password;

-- 验证手机号格式
SELECT 
    user_num,
    phone,
    CASE 
        WHEN phone REGEXP '^1[3-9][0-9]{9}$' THEN '✓ 正确'
        ELSE '✗ 错误'
    END AS '手机号检查'
FROM user
WHERE NOT phone REGEXP '^1[3-9][0-9]{9}$';

COMMIT;
