# Student Management System

## Quick Start Guide

### Prerequisites
- Java 8 or higher
- Maven 3.6+
- MySQL 8.0+
- MySQL root password: `2847`

### Setup Steps

#### 1. Import Database
```bash
# Run the database import script
./import-database.bat
```

#### 2. Test Database Connection
```bash
# Verify database setup
./test-database.bat
```

#### 3. Start Server
```bash
# Start Tomcat server
./start-server.bat
```

#### 4. Access System
Open browser and visit: `http://localhost:8080/studentmis/`

### Test Accounts

| Role | Username | Password | Name |
|------|----------|----------|------|
| Admin | 1000 | Lcd123 | admin |
| Teacher | 1123 | Lcd123 | 肖兴江 |
| Student | 170340 | Lcd123 | 张三 |

### Features

#### Enhanced Course Management
- ✅ Automatic course code generation
- ✅ Course type classification
- ✅ Theory/Practice hours allocation
- ✅ Automatic hours calculation

#### GPA System
- ✅ Automatic score to GPA conversion
- ✅ Grade level indicators (A, A-, B+, B, B-, C+, C, C-, D, F)
- ✅ Average GPA calculation
- ✅ Color-coded grade display

### Troubleshooting

#### Login Issues
1. Run `test-database.bat` to check database connection
2. Ensure MySQL service is running
3. Verify password is `2847`
4. Check console output for debug information

#### Database Issues
1. Import database using `import-database.bat`
2. Check MySQL service status
3. Verify database name is `studentmis_db`

### Development

#### Build Project
```bash
mvn clean compile package
```

#### Run with Maven
```bash
mvn tomcat7:run
```

### Database Schema
- **user**: User accounts and authentication
- **student**: Student information
- **teacher**: Teacher information  
- **course**: Course details
- **score**: Grade records
- **role**: Role definitions
