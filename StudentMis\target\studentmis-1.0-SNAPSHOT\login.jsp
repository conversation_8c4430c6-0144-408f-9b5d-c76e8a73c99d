<%@ page contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <jsp:include page="include/header_css.jsp" flush="true"/>
  <title>学生成绩管理系统</title>
  <style>
    /* 中文字体优化 */
    body, .login-logo, .form-control, .btn, .alert {
      font-family: "Microsoft YaHei", "微软雅黑", "Helvetica Neue", Helvetica, Arial, sans-serif;
    }

    .login-logo {
      font-size: 2.1rem;
      font-weight: 600;
      color: #495057;
      text-align: center;
      margin-bottom: 20px;
    }

    .form-control {
      font-size: 14px;
      padding: 10px 12px;
    }

    .btn-primary {
      font-size: 16px;
      font-weight: 500;
      padding: 10px 20px;
    }

    .alert-info {
      background-color: #e3f2fd;
      border-color: #bbdefb;
      color: #1976d2;
    }

    /* 错误消息样式 */
    .error-msg {
      color: #dc3545;
      font-size: 14px;
      text-align: center;
      margin-top: 10px;
    }

    /* 修复错误消息显示问题 */
    .alert-danger {
      background-color: #f8d7da !important;
      border-color: #f5c6cb !important;
      color: #721c24 !important;
      padding: 12px !important;
      border-radius: 6px !important;
      margin-bottom: 15px !important;
      font-weight: 500 !important;
    }

    .alert-danger i {
      margin-right: 8px;
    }
  </style>
</head>
<body class="hold-transition login-page">
<div class="login-box">
  <div class="login-logo">
    <i class="fas fa-graduation-cap"></i> 学生成绩管理系统
  </div>
  <!-- /.login-logo -->
  <div class="card">
    <div class="card-body login-card-body">
      <form action="Login" method="post" onsubmit="return verify()" id="form1">
        <div class="input-group mb-3">
          <input name="txtUserName" type="text" class="form-control" placeholder="请输入学工号" value="<%= session.getAttribute("user_num")==null?"":session.getAttribute("user_num")%>" >
          <div class="input-group-append">
            <div class="input-group-text">
              <span class="fas fa-user"></span>
            </div>
          </div>
        </div>
        <div class="input-group mb-3">
          <input name="txtPassword" type="password" class="form-control" placeholder="请输入密码" value="<%= session.getAttribute("password")==null?"":session.getAttribute("password")%>">
          <div class="input-group-append">
            <div class="input-group-text">
              <span class="fas fa-lock"></span>
            </div>
          </div>
        </div>
        <div class="row">
          <!-- /.col -->
          <div class="col-3"></div>
          <div class="col-6">
            <input type="submit" class="btn btn-primary btn-block" value="登录">
          </div>
          <div class="col-3"></div>
          <!-- /.col -->
        </div>
        <div class="row">
          <div class="col-12">
            <%
              Object obj = request.getSession().getAttribute("errorMsg");
              if (obj != null && !obj.toString().trim().isEmpty()) {
            %>
            <div class="alert alert-danger error-msg" role="alert">
              <i class="fas fa-exclamation-triangle"></i> <%=obj%>
            </div>
            <%
                // 清除错误消息，避免重复显示
                request.getSession().removeAttribute("errorMsg");
              }
            %>
          </div>
        </div>
      </form>
      <div class="row">
          <div class="col-9">
            <a href="forgot_password.jsp" class="text-center">忘记密码?</a>
          </div>
          <div class="col-3">
          <a href="register.jsp" class="text-center">注册账号</a>
          </div>
      </div>


    </div>
    <!-- /.login-card-body -->
  </div>
</div>
<!-- /.login-box -->
</body>
</html>
<%@include file="include/foot_js.jsp"%>
<script type="text/javascript">
  //绑定按钮事件
  function verify() {
    console.log(`click`);
    //对数据进行检验
    let txtUserName=$(`input[name=txtUserName]`).val();
    if(txtUserName==='')
    {
      alert(`登录名称不能为空`);
      $(`input[name=txtUserName]`).focus();//光标选中
      return false;
    }
    let txtPassword=$(`input[name=txtPassword]`).val();
    if(txtPassword==='')
    {
      alert(`密码不能为空`);
      $(`input[name=txtPassword]`).focus();//光标选中
      return false;
    }
  }
</script>
