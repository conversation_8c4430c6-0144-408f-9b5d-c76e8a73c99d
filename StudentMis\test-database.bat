@echo off
echo ========================================
echo   Database Connection Test Tool
echo ========================================
echo.
echo Testing database connection...
echo.

echo 1. Testing MySQL service status...
mysql -u root -pxhxabc -e "SELECT 'MySQL connection successful!' AS status;" 2>nul
if %errorlevel% == 0 (
    echo MySQL service is running
) else (
    echo MySQL connection failed, please check:
    echo    - MySQL service is running
    echo    - Password is xhxabc
    echo    - Port 3306 is available
    goto :end
)

echo.
echo 2. Checking if database exists...
mysql -u root -pxhxabc -e "USE studentmis_db; SELECT 'studentmis_db database exists!' AS status;" 2>nul
if %errorlevel% == 0 (
    echo studentmis_db database exists
) else (
    echo studentmis_db database does not exist, please import database first
    echo    Run: import-database.bat
    goto :end
)

echo.
echo 3. Checking user table data...
mysql -u root -pxhxabc studentmis_db -e "SELECT user_num, user_name, password, role_id FROM user WHERE user_num='1000';" 2>nul
if %errorlevel% == 0 (
    echo User table data is normal
    echo.
    echo Admin account information:
    mysql -u root -pxhxabc studentmis_db -e "SELECT user_num AS 'Username', user_name AS 'Name', password AS 'Password', CASE role_id WHEN 0 THEN 'Student' WHEN 1 THEN 'Teacher' WHEN 2 THEN 'Admin' END AS 'Role' FROM user WHERE user_num IN ('1000', '1123', '170340');"
) else (
    echo User table data is abnormal
)

:end
echo.
echo ========================================
pause
